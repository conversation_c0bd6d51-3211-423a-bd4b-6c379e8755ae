{"name": "@signalapp/libsignal-client", "version": "0.78.0", "license": "AGPL-3.0-only", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/acknowledgments.md", "dist/*.js", "dist/*.d.ts", "dist/net/**/*.js", "dist/net/**/*.d.ts", "dist/zkgroup/**/*.js", "dist/zkgroup/**/*.d.ts", "Native.js", "Native.d.ts", "zkgroup.js", "zkgroup.d.ts", "prebuilds/*/*.node"], "scripts": {"install": "echo Use \\`npx node-gyp rebuild\\` to build the native library from scratch if needed", "build": "node-gyp build", "build-with-debug-level-logs": "npx --libsignal-debug-level-logs node-gyp build", "tsc": "tsc -b", "clean": "rimraf dist build prebuilds", "test": "mocha --recursive dist/test --require source-map-support/register", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "p() { prettier ${@:- --write} '**/*.{css,js,json,md,scss,ts,tsx}' ../rust/bridge/node/bin/Native.d.ts.in; }; p", "format-check": "p() { prettier ${@:- --check} '**/*.{css,js,json,md,scss,ts,tsx}' ../rust/bridge/node/bin/Native.d.ts.in; }; p", "prepack": "cp ../acknowledgments/acknowledgments-desktop.md dist/acknowledgments.md"}, "dependencies": {"node-gyp-build": "^4.8.0", "type-fest": "^4.26.0", "uuid": "^11"}, "devDependencies": {"@indutny/bencher": "^1.2.0", "@signalapp/mock-server": "^11.1.0", "@types/bindings": "^1.3.0", "@types/chai": "^4.3.1", "@types/chai-as-promised": "^7.1.3", "@types/chance": "^1.1.3", "@types/mocha": "^5.2.7", "@types/node": "~20.11.0", "@types/sinon": "^17.0.3", "@types/sinon-chai": "^3.2.12", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "chance": "^1.1.11", "eslint": "^8.30.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-chai-expect": "^3.1.0", "eslint-plugin-header": "^3.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^48.2.0", "eslint-plugin-mocha": "^10.1.0", "eslint-plugin-more": "^1.0.0", "mocha": "^9", "node-gyp": "^11.2.0", "prebuildify": "^6.0.0", "prettier": "^2.7.1", "rimraf": "^6.0.1", "sinon": "^18.0.0", "sinon-chai": "^3.7.0", "source-map-support": "^0.5.19", "typescript": "5.3.3"}}