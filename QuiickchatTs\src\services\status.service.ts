import { Status, StatusView, IStatus } from '../models/status.model';
import { User } from '../models/user.model';
import * as contactService from './contact.service';
import { DatabaseError, ValidationError, NotFoundError } from '../utils/errors';
import { logger } from '../utils/logger';
import {
  CreateStatusRequest,
  StatusResponse,
  StatusSummary
} from '../types';
import { wsManager } from './websocket.service';

// Cron job to mark expired statuses as inactive
export const markExpiredStatusesInactive = async (): Promise<void> => {
  try {
    const now = new Date();
    const result = await Status.updateMany(
      {
        is_active: true,
        expires_at: { $lte: now }
      },
      {
        $set: { is_active: false }
      }
    );

    if (result.modifiedCount > 0) {
      logger.info(`Marked ${result.modifiedCount} expired statuses as inactive`);
    }
  } catch (error) {
    logger.error('Error marking expired statuses inactive:', error);
  }
};

export const createStatus = async (userId: string, statusData: CreateStatusRequest): Promise<StatusResponse> => {
  try {
    // For text status, require either content or caption
    if (statusData.content_type === 'text' && !statusData.content && !statusData.caption) {
      throw new ValidationError('Text status requires content or caption');
    }

    if ((statusData.content_type === 'image' || statusData.content_type === 'video') && !statusData.content_url) {
      throw new ValidationError('Media status requires content URL');
    }

    const status = new Status({
      user_id: userId,
      content_type: statusData.content_type,
      content: statusData.content, // For text status content
      content_url: statusData.content_url, // For image/video URLs
      caption: statusData.caption,
      background_color: statusData.background_color,
      text_color: statusData.text_color, // Add text color support
      font_style: statusData.font_style,
      privacy_setting: statusData.privacy_setting || 'Contacts',
      allowed_contacts: statusData.allowed_contacts || [],
      blocked_contacts: statusData.blocked_contacts || [],
      view_count: 0,
      viewers: [],
      is_active: true,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    });

    await status.save();

    logger.info(`Status created for user ${userId}: ${status._id}`);

    // Get user's contacts to broadcast status update
    const user = await User.findOne({ $or: [{ _id: userId }, { phone: userId }] });
    if (user) {
      // Broadcast status update to contacts via WebSocket (async but don't wait)
      wsManager.broadcastStatusUpdate(user.phone, {
        id: status._id.toString(),
        user_id: status.user_id,
        content_type: status.content_type,
        content: status.content,
        content_url: status.content_url,
        caption: status.caption,
        background_color: status.background_color,
        text_color: status.text_color,
        font_style: status.font_style,
        privacy_setting: status.privacy_setting,
        view_count: status.view_count,
        is_active: status.is_active,
        expires_at: status.expires_at,
        created_at: status.createdAt
      }).catch(error => {
        logger.error('Failed to broadcast status update:', error);
      });
    }

    const statusResponse = {
      id: status._id.toString(),
      user_id: status.user_id,
      content_type: status.content_type,
      content: status.content || undefined, // For text status content
      content_url: status.content_url || undefined, // For image/video URLs
      caption: status.caption || undefined,
      background_color: status.background_color || undefined,
      text_color: status.text_color || undefined, // Add text color support
      font_style: status.font_style || undefined,
      privacy_setting: status.privacy_setting,
      view_count: status.view_count,
      is_active: status.is_active,
      expires_at: status.expires_at,
      created_at: status.createdAt!
    };

    return statusResponse;
  } catch (error) {
    logger.error('Failed to create status:', error);
    throw new DatabaseError(`Failed to create status: ${error}`);
  }
};

export const getUserStatuses = async (userId: string, viewerId?: string): Promise<StatusResponse[]> => {
  try {
    const statuses = await Status.find({
      user_id: userId,
      is_active: true,
      expires_at: { $gt: new Date() }
    }).sort({ createdAt: -1 });

    const statusResponses: StatusResponse[] = [];

    for (const status of statuses) {
      const statusResponse: StatusResponse = {
        id: status._id.toString(),
        user_id: status.user_id,
        content_type: status.content_type,
        content_url: status.content_url || undefined,
        caption: status.caption || undefined,
        background_color: status.background_color || undefined,
        font_style: status.font_style || undefined,
        privacy_setting: status.privacy_setting,
        view_count: status.view_count,
        is_active: status.is_active,
        expires_at: status.expires_at,
        created_at: status.createdAt!
      };

      if (viewerId && !canViewStatus(status, viewerId)) {
        continue;
      }

      statusResponses.push(statusResponse);
    }

    return statusResponses;
  } catch (error) {
    logger.error('Failed to get user statuses:', error);
    throw new DatabaseError(`Failed to get user statuses: ${error}`);
  }
};

export const getContactStatuses = async (userId: string): Promise<StatusSummary[]> => {
  try {
    const registeredContactIds = await contactService.getRegisteredContacts(userId);
    
    if (registeredContactIds.length === 0) {
      return [];
    }

    const summaries: StatusSummary[] = [];

    for (const contactId of registeredContactIds) {
      const statuses = await Status.find({
        user_id: contactId,
        is_active: true,
        expires_at: { $gt: new Date() }
      }).sort({ createdAt: -1 });

      if (statuses.length === 0) {
        continue;
      }

      const viewableStatuses = statuses.filter(status => canViewStatus(status, userId));
      
      if (viewableStatuses.length === 0) {
        continue;
      }

      const unviewedCount = await getUnviewedStatusCount(contactId, userId);
      const latestStatus = viewableStatuses[0];

      if (!latestStatus || !latestStatus.createdAt) {
        continue;
      }

      const summary: StatusSummary = {
        user_id: contactId,
        total_statuses: viewableStatuses.length,
        unviewed_statuses: unviewedCount,
        latest_status_time: latestStatus.createdAt
      };

      try {
        const user = await User.findById(contactId);
        if (user) {
          summary.profile_picture = user.profile_picture;
        }
      } catch (error) {
        logger.warn(`Failed to get user details for contact ${contactId}:`, error);
      }

      summaries.push(summary);
    }

    summaries.sort((a, b) => b.latest_status_time.getTime() - a.latest_status_time.getTime());

    return summaries;
  } catch (error) {
    logger.error('Failed to get contact statuses:', error);
    throw new DatabaseError(`Failed to get contact statuses: ${error}`);
  }
};

export const viewStatus = async (statusId: string, viewerId: string): Promise<StatusResponse> => {
  try {
    const status = await Status.findById(statusId);
    
    if (!status) {
      throw new NotFoundError('Status not found');
    }

    if (!status.is_active || status.expires_at <= new Date()) {
      throw new NotFoundError('Status is no longer available');
    }

    if (!canViewStatus(status, viewerId)) {
      throw new ValidationError('You do not have permission to view this status');
    }

    const existingView = await StatusView.findOne({
      status_id: statusId,
      viewer_id: viewerId
    });

    if (!existingView) {
      await StatusView.create({
        status_id: statusId,
        viewer_id: viewerId,
        viewed_at: new Date()
      });

      if (!status.viewers.includes(viewerId)) {
        status.viewers.push(viewerId);
        status.view_count = status.viewers.length;
        await status.save();

        wsManager.broadcastStatusView(statusId, viewerId, status.user_id);
      }
    }

    return {
      id: status._id.toString(),
      user_id: status.user_id,
      content_type: status.content_type,
      content_url: status.content_url || undefined,
      caption: status.caption || undefined,
      background_color: status.background_color || undefined,
      font_style: status.font_style || undefined,
      privacy_setting: status.privacy_setting,
      view_count: status.view_count,
      is_active: status.is_active,
      expires_at: status.expires_at,
      created_at: status.createdAt!
    };
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof ValidationError) {
      throw error;
    }
    logger.error('Failed to view status:', error);
    throw new DatabaseError(`Failed to view status: ${error}`);
  }
};

const canViewStatus = (status: IStatus, viewerId: string): boolean => {
  if (status.user_id === viewerId) {
    return true;
  }

  switch (status.privacy_setting) {
    case 'Everyone':
      return true;
    case 'Contacts':
      return !status.blocked_contacts.includes(viewerId);
    case 'ContactsExcept':
      return !status.blocked_contacts.includes(viewerId);
    case 'OnlyShare':
      return status.allowed_contacts.includes(viewerId);
    default:
      return false;
  }
};

const getUnviewedStatusCount = async (statusOwnerId: string, viewerId: string): Promise<number> => {
  try {
    const statuses = await Status.find({
      user_id: statusOwnerId,
      is_active: true,
      expires_at: { $gt: new Date() }
    });

    const viewableStatuses = statuses.filter(status => canViewStatus(status, viewerId));

    let unviewedCount = 0;
    for (const status of viewableStatuses) {
      const hasViewed = await StatusView.exists({
        status_id: status._id.toString(),
        viewer_id: viewerId
      });

      if (!hasViewed) {
        unviewedCount++;
      }
    }

    return unviewedCount;
  } catch (error) {
    logger.error('Failed to get unviewed status count:', error);
    return 0;
  }
};

export const getStatusViews = async (statusId: string, requesterId: string): Promise<any[]> => {
  try {
    const status = await Status.findById(statusId);

    if (!status) {
      throw new NotFoundError('Status not found');
    }

    // Only the status owner can see who viewed their status
    if (status.user_id !== requesterId) {
      throw new ValidationError('You can only view the viewers of your own statuses');
    }

    const views = await StatusView.find({ status_id: statusId })
      .sort({ viewed_at: -1 });

    // Get user details for each viewer
    const viewsWithUserDetails = await Promise.all(
      views.map(async (view) => {
        const user = await User.findOne({ phone: view.viewer_id });
        return {
          viewer_id: view.viewer_id,
          viewed_at: view.viewed_at,
          display_name: user?.username || undefined,
          phone_number: user?.phone || undefined,
          profile_picture: user?.profile_picture || undefined
        };
      })
    );

    return viewsWithUserDetails;
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof ValidationError) {
      throw error;
    }
    logger.error('Failed to get status views:', error);
    throw new DatabaseError(`Failed to get status views: ${error}`);
  }
};

export const deleteStatus = async (statusId: string, userId: string): Promise<void> => {
  try {
    const status = await Status.findById(statusId);

    if (!status) {
      throw new NotFoundError('Status not found');
    }

    // Only the status owner can delete their status
    if (status.user_id !== userId) {
      throw new ValidationError('You can only delete your own statuses');
    }

    // Soft delete by setting is_active to false
    await Status.findByIdAndUpdate(statusId, {
      is_active: false,
      updatedAt: new Date()
    });

    // Also delete all views for this status
    await StatusView.deleteMany({ status_id: statusId });

    // Broadcast deletion to contacts (async but don't wait)
    try {
      await wsManager.broadcastStatusDeletion(statusId, userId);
    } catch (error: any) {
      logger.error('Failed to broadcast status deletion:', error);
    }

    logger.info(`Status deleted: ${statusId} by user: ${userId}`);

    // Broadcast status deletion to contacts
    wsManager.broadcastStatusDeletion(userId, statusId);
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof ValidationError) {
      throw error;
    }
    logger.error('Failed to delete status:', error);
    throw new DatabaseError(`Failed to delete status: ${error}`);
  }
};

export const getPrivacySettings = async (userId: string): Promise<any> => {
  try {
    const user = await User.findOne({ phone: userId });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Return default privacy settings if not set
    return {
      default_privacy_setting: user.status_privacy_setting || 'Contacts',
      blocked_contacts: user.blocked_contacts || [],
      allowed_contacts: user.allowed_contacts || []
    };
  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }
    logger.error('Failed to get privacy settings:', error);
    throw new DatabaseError(`Failed to get privacy settings: ${error}`);
  }
};

export const updatePrivacySettings = async (userId: string, settings: any): Promise<any> => {
  try {
    const user = await User.findOne({ phone: userId });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    const updateData: any = {};

    if (settings.default_privacy_setting) {
      if (!['Everyone', 'Contacts', 'ContactsExcept', 'OnlyShare'].includes(settings.default_privacy_setting)) {
        throw new ValidationError('Invalid privacy setting');
      }
      updateData.status_privacy_setting = settings.default_privacy_setting;
    }

    if (settings.blocked_contacts !== undefined) {
      updateData.blocked_contacts = settings.blocked_contacts;
    }

    if (settings.allowed_contacts !== undefined) {
      updateData.allowed_contacts = settings.allowed_contacts;
    }

    await User.findOneAndUpdate({ phone: userId }, updateData);

    logger.info(`Privacy settings updated for user: ${userId}`);

    return {
      default_privacy_setting: updateData.status_privacy_setting || user.status_privacy_setting || 'Contacts',
      blocked_contacts: updateData.blocked_contacts || user.blocked_contacts || [],
      allowed_contacts: updateData.allowed_contacts || user.allowed_contacts || []
    };
  } catch (error) {
    if (error instanceof NotFoundError || error instanceof ValidationError) {
      throw error;
    }
    logger.error('Failed to update privacy settings:', error);
    throw new DatabaseError(`Failed to update privacy settings: ${error}`);
  }
};
