# Complete Status Request Examples

## 1. Text Status Request (Full Example)

```http
POST http://localhost:8080/api/v1/status
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "content_type": "text",
  "content": "Having an amazing day! 🌟 Just finished a great workout and feeling energized! 💪",
  "background_color": "#FF6B6B",
  "text_color": "#FFFFFF",
  "font_style": "bold",
  "privacy_setting": "Contacts",
  "allowed_contacts": [],
  "blocked_contacts": []
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Status created successfully",
  "data": {
    "id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "user_id": "+2347049670618",
    "content_type": "text",
    "content": "Having an amazing day! 🌟 Just finished a great workout and feeling energized! 💪",
    "background_color": "#FF6B6B",
    "text_color": "#FFFFFF",
    "font_style": "bold",
    "privacy_setting": "Contacts",
    "view_count": 0,
    "is_active": true,
    "expires_at": "2024-01-16T10:30:00.000Z",
    "created_at": "2024-01-15T10:30:00.000Z"
  }
}
```

## 2. Image Status Upload (Multipart Form)

```http
POST http://localhost:8080/api/v1/status/upload
Content-Type: multipart/form-data
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Form Data Fields:
content_type: image
caption: Beautiful sunset from my balcony! 🌅 Nature is amazing!
background_color: #FF9800
privacy_setting: Contacts
allowed_contacts: []
blocked_contacts: []
status_media: [SELECT YOUR IMAGE FILE]
```

## 3. Video Status Upload

```http
POST http://localhost:8080/api/v1/status/upload
Content-Type: multipart/form-data
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Form Data Fields:
content_type: video
caption: Check out this amazing dance performance! 💃🕺
privacy_setting: Everyone
allowed_contacts: []
blocked_contacts: []
status_media: [SELECT YOUR VIDEO FILE - MAX 30 SECONDS]
```

## 4. Private Status (Only Specific Contacts)

```http
POST http://localhost:8080/api/v1/status
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "content_type": "text",
  "content": "Private message for close friends only! 🤫",
  "background_color": "#9C27B0",
  "text_color": "#FFFFFF",
  "font_style": "italic",
  "privacy_setting": "OnlyShare",
  "allowed_contacts": ["+**********", "+1987654321"],
  "blocked_contacts": []
}
```

## 5. Status with Blocked Contacts

```http
POST http://localhost:8080/api/v1/status
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "content_type": "text",
  "content": "Sharing with most contacts but not everyone 😊",
  "background_color": "#4CAF50",
  "text_color": "#FFFFFF",
  "font_style": "normal",
  "privacy_setting": "ContactsExcept",
  "allowed_contacts": [],
  "blocked_contacts": ["+1555123456", "+1555987654"]
}
```

## Privacy Settings Explained

- **"Everyone"**: Visible to all users (public)
- **"Contacts"**: Visible to your contacts only
- **"ContactsExcept"**: Visible to contacts except those in blocked_contacts
- **"OnlyShare"**: Visible only to contacts listed in allowed_contacts

## Font Styles Available

- **"normal"**: Regular text
- **"bold"**: Bold text
- **"italic"**: Italic text

## Color Format

Use hex color codes:
- Background: `"#FF6B6B"` (red)
- Text: `"#FFFFFF"` (white)
- Examples: `#4CAF50` (green), `#2196F3` (blue), `#FF9800` (orange)

## Complete Authentication Flow

### 1. Register User
```http
POST http://localhost:8080/api/v1/auth/register
Content-Type: application/json

{
  "phone": "+2347049670618"
}
```

### 2. Verify Registration (use code from SMS or logs)
```http
POST http://localhost:8080/api/v1/auth/verify
Content-Type: application/json

{
  "phone": "+2347049670618",
  "code": "123456"
}
```

### 3. Use the access_token from step 2 in Authorization header
```
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE
```

## Testing SMS Service

### Check SMS Service Status
```http
GET http://localhost:8080/api/v1/system/sms-status
```

### Test SMS Sending
```http
POST http://localhost:8080/api/v1/system/test-sms
Content-Type: application/json

{
  "phone": "+**********"
}
```

## Common Issues & Solutions

### 1. SMS Not Sending
- Check Twilio credentials in `.env` file
- Verify phone number format (+**********)
- Check Twilio account balance
- Run: `node scripts/fix-twilio.js`

### 2. Status Creation Fails
- Ensure you have a valid access token
- Check that content_type is one of: "text", "image", "video"
- For text status, provide either "content" or "caption"
- For media status, use the upload endpoint with multipart/form-data

### 3. Authentication Issues
- Make sure phone number is in international format
- Check that verification code is correct and not expired
- Verify JWT token is included in Authorization header

### 4. File Upload Issues
- Use multipart/form-data content type
- Ensure file size is within limits (images: 5MB, videos: 25MB)
- Check file format (images: JPG/PNG, videos: MP4/MOV)
- Videos are automatically limited to 30 seconds for status
