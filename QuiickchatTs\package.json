{"name": "quickchat-backend-ts", "version": "1.0.0", "description": "QuickChat backend application built with Fastify and TypeScript", "main": "dist/index.js", "scripts": {"dev": "nodemon", "build": "npx tsc", "build:prod": "node build.js", "build:render": "node build.js", "start": "node dist/index.js", "start:dev": "node -r tsconfig-paths/register dist/index.js", "test": "jest", "test:qr": "node scripts/device-fingerprint.js", "test:fingerprint": "node scripts/device-fingerprint.js fingerprint", "test:qr-login": "node scripts/device-fingerprint.js qr-login", "test:security": "node scripts/device-fingerprint.js security-test", "test:watch": "jest --watch", "test:zengo": "node run_zengo_tests.js", "test:zengo-api": "node run_zengo_tests.js --api-only", "test:zengo-local": "node run_zengo_tests.js --local-only", "zengo:enable": "node enable_zengo.js", "zengo:disable": "node disable_zengo.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "memory:check": "node scripts/memory-check.js", "memory:monitor": "node scripts/memory-check.js --monitor", "fix:imports": "node scripts/fix-imports.js"}, "keywords": ["quickchat", "messaging", "fastify", "typescript", "mongodb"], "author": "Olajosh80", "license": "MIT", "dependencies": {"@fastify/cors": "^8.4.0", "@fastify/multipart": "^8.0.0", "@fastify/static": "^6.12.0", "@fastify/websocket": "^10.0.1", "@signalapp/libsignal-client": "^0.78.0", "@types/node-cron": "^3.0.11", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "cloudinary-build-url": "^0.2.4", "dotenv": "^16.3.1", "fastify": "^4.24.3", "jimp": "^1.6.0", "jsonwebtoken": "^9.0.2", "jsqr": "^1.4.0", "mongoose": "^8.0.3", "node-cron": "^4.2.1", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "qrcode": "^1.5.4", "sodium-native": "^5.0.6", "tsconfig-paths": "^4.2.0", "twilio": "^4.19.0", "uuid": "^9.0.1", "validator": "^13.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.9.0", "@types/qrcode": "^1.5.5", "@types/sodium-native": "^2.3.9", "@types/uuid": "^9.0.7", "@types/validator": "^13.11.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsc-alias": "^1.8.16", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}