# Test SMS Service - QuickChat API
# Use this file with REST Client extension in VS Code or similar HTTP client

### 1. Test SMS Service Status
GET http://localhost:8080/api/v1/system/sms-status
Content-Type: application/json

### 2. Test SMS Sending (replace with your phone number)
POST http://localhost:8080/api/v1/system/test-sms
Content-Type: application/json

{
  "phone": "+1234567890"
}

### 3. Test User Registration (this will trigger SMS)
POST http://localhost:8080/api/v1/auth/register
Content-Type: application/json

{
  "phone": "+1234567890"
}

### 4. Test User Login (this will also trigger SMS)
POST http://localhost:8080/api/v1/auth/login
Content-Type: application/json

{
  "phone": "+1234567890"
}

### 5. Create Status (example request with full status)
POST http://localhost:8080/api/v1/status
Content-Type: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

{
  "content_type": "text",
  "content": "My new status update! 🎉",
  "background_color": "#FF5733",
  "text_color": "#FFFFFF",
  "font_style": "bold",
  "privacy_setting": "Contacts",
  "allowed_contacts": [],
  "blocked_contacts": []
}

### 6. Upload Status Media (example for image/video)
POST http://localhost:8080/api/v1/status/upload
Content-Type: multipart/form-data
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

# Form data:
# content_type: image
# caption: Beautiful sunset today!
# privacy_setting: Contacts
# status_media: [your image file]
