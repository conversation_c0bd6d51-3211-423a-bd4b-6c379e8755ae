# Postman Collection Updates Summary

## Overview
The QuiickchatTs API Postman collection has been completely updated to match the actual backend routes and include all required parameters, request bodies, and query parameters.

## Key Changes Made

### 1. Collection Metadata
- **Version**: Updated from 1.0.0 to 2.0.0
- **Base URL**: Changed from `http://localhost:8080` to `http://localhost:3000`
- **New Variables**: Added `session_id`, `status_id`, and `device_fingerprint`

### 2. Authentication Routes (/api/v1/auth)
- ✅ **Added**: `GET /auth/init` - Initialize User endpoint
- ✅ **Updated**: All request bodies to match TypeScript interfaces
- ✅ **Fixed**: Response handling scripts for token extraction

### 3. User Routes (/api/v1/users)
- ✅ **Updated**: `GET /users/me` with proper query parameters (`phone`, `user_id`)
- ✅ **Fixed**: Profile picture upload with correct form field name
- ✅ **Updated**: All request bodies to match backend models

### 4. Status Routes (/api/v1/status) - MAJOR UPDATES
- ✅ **Updated**: `POST /status` with complete CreateStatusRequest structure
- ✅ **Enhanced**: Media upload with all optional parameters
- ✅ **Fixed**: Route paths (`/my` instead of `/me`, `/feed` instead of `/contacts`)
- ✅ **Added**: Missing endpoints:
  - `GET /status/user/:user_id` - Get statuses for specific user
  - `GET /status/:status_id/views` - Get who viewed a status
  - `DELETE /status/:status_id` - Delete a status
  - `GET /status/privacy` - Get privacy settings
  - `PUT /status/privacy` - Update privacy settings

### 5. QR Authentication Routes (/api/v1/qr) - NEW SECTION
- ✅ **Added**: Complete QR authentication workflow:
  - `POST /qr/generate` - Generate QR code for login/device linking
  - `GET /qr/status/:sessionId` - Check QR status
  - `POST /qr/scan` - Scan QR code (authenticated)
  - `POST /qr/approve` - Approve QR authentication
  - `POST /qr/reject` - Reject QR authentication
  - `POST /qr/device/initiate` - Initiate device linking
  - `POST /qr/device/verify` - Verify device link
  - `POST /qr/device/complete` - Complete device link
  - `POST /qr/device/revoke` - Revoke linked device
  - `GET /qr/devices/list` - List linked devices

### 6. Deep Links Routes (/api/v1/link) - NEW SECTION
- ✅ **Added**: Complete deep link functionality:
  - `POST /link/generate` - Generate deep links
  - `POST /link/parse` - Parse deep links
  - `GET /link/qr-scan/:sessionId` - QR scan redirect
  - `GET /link/device-link/:sessionId` - Device link redirect
  - `GET /link/app-store` - Get app store links
  - `GET /link/smart-banner/:sessionId/:action` - Generate smart banner
  - `GET /link/validate/:sessionId/:action` - Validate deep link session

### 7. System Routes (/api/v1/system) - NEW SECTION
- ✅ **Added**: System monitoring endpoints:
  - `GET /system/sms-status` - SMS service status
  - `POST /system/test-sms` - Test SMS service
  - `GET /system/health` - System health check

### 8. Zengo Routes (/api/v1/z) - MAJOR FIXES
- ✅ **Fixed**: All request bodies to match Zengo API specification
- ✅ **Updated**: Query parameters with correct casing (e.g., `UserIds` instead of `user_ids`)
- ✅ **Corrected**: Request structures:
  - User registration: `UserInfo` array with `UserId`, `UserName`, `UserAvatar`
  - Friends operations: `FromUserId`, `FriendInfos` with proper attributes
  - Blocking operations: `FromUserId`, `ToUserIdList`
  - Query parameters: `FromUserId`, `StartIndex`, etc.

### 9. Contact Routes (/api/v1/contacts)
- ✅ **Verified**: All endpoints match backend implementation
- ✅ **Updated**: Request bodies to match `BulkContactsRequest` interface

## Request Body Examples

### Status Creation (Text)
```json
{
  "content_type": "text",
  "content": "My new status update!",
  "background_color": "#FF5733",
  "text_color": "#FFFFFF",
  "font_style": "bold",
  "privacy_setting": "Contacts",
  "allowed_contacts": [],
  "blocked_contacts": []
}
```

### QR Code Generation
```json
{
  "session_type": "login",
  "device_fingerprint": "web-browser-fingerprint-123"
}
```

### Zengo User Registration
```json
{
  "UserInfo": [
    {
      "UserId": "user123",
      "UserName": "John Doe",
      "UserAvatar": "https://example.com/avatar.jpg"
    }
  ]
}
```

## Authentication
- All protected endpoints use Bearer token authentication
- Tokens are automatically extracted and stored in collection variables
- Public endpoints (health checks, QR status) have `noauth` specified

## Variables Available
- `{{base_url}}` - API base URL
- `{{access_token}}` - JWT access token
- `{{refresh_token}}` - JWT refresh token
- `{{phone_number}}` - User phone number
- `{{user_id}}` - User ID
- `{{session_id}}` - QR/Device session ID
- `{{status_id}}` - Status ID
- `{{device_fingerprint}}` - Device fingerprint

## Testing Notes
1. Start with Authentication → Initialize User
2. Register/Login to get tokens
3. Use the tokens for protected endpoints
4. QR authentication requires mobile app integration
5. Zengo endpoints require Zengo service to be enabled

The collection now provides complete coverage of all API endpoints with proper request structures and parameters.
