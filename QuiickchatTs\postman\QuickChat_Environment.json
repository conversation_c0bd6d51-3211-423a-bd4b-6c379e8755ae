{"id": "quickchat-env-v3", "name": "QuickChat Environment v3.0", "values": [{"key": "base_url", "value": "http://localhost:8080/api/v1", "description": "Base URL for QuickChat API (Development)", "type": "default", "enabled": true}, {"key": "production_url", "value": "https://your-domain.com/api/v1", "description": "Production URL for QuickChat API", "type": "default", "enabled": false}, {"key": "websocket_url", "value": "ws://localhost:8080/status-updates", "description": "WebSocket URL for real-time updates", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "description": "JWT Access Token (auto-set from auth responses)", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "description": "JWT Refresh <PERSON> (auto-set from auth responses)", "type": "secret", "enabled": true}, {"key": "user_phone", "value": "", "description": "Current authenticated user's phone number", "type": "default", "enabled": true}, {"key": "phone_number", "value": "+2347049670618", "description": "Test phone number for registration/login", "type": "default", "enabled": true}, {"key": "verification_code", "value": "123456", "description": "SMS verification code (auto-set in dev mode)", "type": "default", "enabled": true}, {"key": "test_contact_phone", "value": "+1234567890", "description": "Test contact phone number", "type": "default", "enabled": true}, {"key": "test_status_id", "value": "", "description": "Test status ID (auto-set from status creation)", "type": "default", "enabled": true}, {"key": "qr_session_id", "value": "", "description": "QR authentication session ID", "type": "default", "enabled": true}, {"key": "test_contact_id", "value": "", "description": "Test contact ID for API calls", "type": "default", "enabled": true}, {"key": "test_group_id", "value": "", "description": "Test group ID for API calls", "type": "default", "enabled": true}, {"key": "api_version", "value": "v1", "description": "API version", "type": "default", "enabled": true}, {"key": "content_type_json", "value": "application/json", "description": "JSON content type header", "type": "default", "enabled": true}, {"key": "content_type_form", "value": "multipart/form-data", "description": "Form data content type header", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-15T10:30:00.000Z", "_postman_exported_using": "Postman/10.20.0"}