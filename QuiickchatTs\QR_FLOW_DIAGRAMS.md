# 📊 QuickChat QR Authentication Flow Diagrams

## 🔄 Complete Authentication Flow

```mermaid
sequenceDiagram
    participant D as Desktop/Web
    participant S as QuickChat Server
    participant M as Mobile App
    participant U as User

    Note over D,U: QR Login Process

    D->>S: 1. Generate QR Request
    Note right of D: POST /api/v1/qr/generate<br/>+ device fingerprint
    
    S->>S: 2. Create Session
    Note right of S: Generate session ID<br/>Create challenge & nonce<br/>Set 2-min expiry
    
    S->>D: 3. QR Code Data
    Note right of S: QR code + session ID<br/>+ expiry time
    
    D->>D: 4. Display QR
    Note right of D: Show QR code<br/>Start countdown timer<br/>Begin status polling
    
    loop Every 2 seconds
        D->>S: 5. Check Status
        Note right of D: GET /api/v1/qr/status/{sessionId}
        S->>D: Status: "pending"
    end
    
    M->>M: 6. Open QR Scanner
    Note right of M: User opens camera<br/>in QuickChat app
    
    M->>D: 7. Scan QR Code
    Note right of M: Camera reads QR<br/>Extracts session data
    
    M->>S: 8. Submit <PERSON>an
    Note right of M: POST /api/v1/qr/scan<br/>+ JWT token + QR data
    
    S->>S: 9. Validate Scan
    Note right of S: Check session exists<br/>Verify not expired<br/>Update status to "scanned"
    
    S->>M: 10. Scan Success
    Note right of S: Return session details<br/>+ challenge data
    
    M->>U: 11. Show Approval Dialog
    Note right of M: "Allow Desktop Access?"<br/>Device info + location
    
    U->>M: 12. User Decision
    Note right of U: Tap "Approve" or "Reject"
    
    alt User Approves
        M->>S: 13a. Approve Request
        Note right of M: POST /api/v1/qr/approve<br/>session_id + approved: true
        
        S->>S: 14a. Update Status
        Note right of S: Status = "approved"<br/>Generate auth tokens
        
        D->>S: 15a. Status Check
        Note right of D: Regular polling detects<br/>status change
        
        S->>D: 16a. Login Success
        Note right of S: Status: "approved"<br/>+ auth tokens
        
        D->>D: 17a. Complete Login
        Note right of D: Store tokens<br/>Redirect to chat<br/>Show success message
        
    else User Rejects
        M->>S: 13b. Reject Request
        Note right of M: POST /api/v1/qr/approve<br/>session_id + approved: false
        
        S->>S: 14b. Update Status
        Note right of S: Status = "rejected"
        
        D->>S: 15b. Status Check
        S->>D: 16b. Login Rejected
        Note right of S: Status: "rejected"
        
        D->>D: 17b. Handle Rejection
        Note right of D: Show error message<br/>Generate new QR code
    end
```

## 🔐 Device Linking Flow

```mermaid
sequenceDiagram
    participant D as Desktop/Web
    participant S as QuickChat Server
    participant M as Mobile App (Primary)
    participant U as User

    Note over D,U: Device Linking Process

    D->>S: 1. Initiate Device Link
    Note right of D: POST /api/v1/qr/device/initiate<br/>+ device info + crypto keys
    
    S->>S: 2. Create Link Session
    Note right of S: Generate verification code<br/>Create device record<br/>Set expiry (5 minutes)
    
    S->>D: 3. Link QR Code
    Note right of S: QR with session ID<br/>+ verification code
    
    D->>D: 4. Display Link QR
    Note right of D: Show QR + verification code<br/>Start status polling
    
    M->>D: 5. Scan Link QR
    Note right of M: User scans with<br/>primary device
    
    M->>S: 6. Submit Link Scan
    Note right of M: POST /api/v1/qr/scan<br/>+ device link data
    
    S->>M: 7. Show Link Details
    Note right of S: Device name, type, location<br/>Security information
    
    M->>U: 8. Link Approval Dialog
    Note right of M: "Link John's MacBook?"<br/>Device details + permissions
    
    U->>M: 9. Approve Link
    Note right of U: User confirms device link
    
    M->>S: 10. Confirm Link
    Note right of M: POST /api/v1/qr/device/complete<br/>+ verification code
    
    S->>S: 11. Complete Link
    Note right of S: Create device record<br/>Generate device tokens<br/>Setup encryption keys
    
    S->>D: 12. Link Success
    Note right of S: Device linked successfully<br/>+ access tokens
    
    D->>D: 13. Setup Complete
    Note right of D: Store device credentials<br/>Enable sync<br/>Show success
```

## 🛡️ Security Validation Flow

```mermaid
flowchart TD
    A[QR Request] --> B{Valid Fingerprint?}
    B -->|No| C[Reject Request]
    B -->|Yes| D{Rate Limit OK?}
    
    D -->|No| E[Rate Limited]
    D -->|Yes| F[Generate Session]
    
    F --> G[Create QR Code]
    G --> H[Start Expiry Timer]
    
    H --> I[QR Displayed]
    I --> J{Mobile Scan?}
    
    J -->|No| K{Expired?}
    K -->|Yes| L[Generate New QR]
    K -->|No| I
    L --> I
    
    J -->|Yes| M{Valid Session?}
    M -->|No| N[Invalid QR Error]
    M -->|Yes| O{User Authenticated?}
    
    O -->|No| P[Auth Required]
    O -->|Yes| Q[Show Approval Dialog]
    
    Q --> R{User Decision?}
    R -->|Approve| S[Login Success]
    R -->|Reject| T[Login Rejected]
    
    S --> U[Generate Tokens]
    T --> V[Generate New QR]
    V --> I
```

## 📱 Mobile App State Machine

```mermaid
stateDiagram-v2
    [*] --> Idle
    
    Idle --> Scanning : Open QR Scanner
    Scanning --> Processing : QR Detected
    Processing --> ShowApproval : Valid QR
    Processing --> Error : Invalid QR
    
    ShowApproval --> Approving : User Approves
    ShowApproval --> Rejecting : User Rejects
    ShowApproval --> Timeout : No Response
    
    Approving --> Success : Server Confirms
    Approving --> Error : Server Error
    
    Rejecting --> Idle : Rejection Sent
    
    Success --> Idle : Complete
    Error --> Idle : Show Error
    Timeout --> Idle : Session Expired
```

## 🔄 Status Polling Cycle

```mermaid
flowchart LR
    A[Start Polling] --> B[Check Status]
    B --> C{Status?}
    
    C -->|pending| D[Wait 2 seconds]
    C -->|scanned| E[Show 'Scanned' Message]
    C -->|approved| F[Login Success]
    C -->|rejected| G[Show Error & Regenerate]
    C -->|expired| H[Auto Regenerate QR]
    
    D --> B
    E --> I[Wait 2 seconds]
    I --> B
    
    F --> J[Stop Polling]
    G --> K[Stop Polling]
    H --> L[Stop Polling]
```

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend (Browser)"
        A[HTML Demo Page]
        B[JavaScript QR Handler]
        C[Status Polling]
        D[Device Fingerprinting]
    end
    
    subgraph "Backend (Node.js)"
        E[Fastify Server]
        F[QR Auth Controller]
        G[QR Auth Service]
        H[Device Link Service]
    end
    
    subgraph "Database (MongoDB)"
        I[QR Sessions]
        J[User Accounts]
        K[Device Links]
        L[Audit Logs]
    end
    
    subgraph "Mobile App"
        M[QR Scanner]
        N[Approval Dialog]
        O[API Client]
    end
    
    A --> B
    B --> C
    B --> D
    
    B <--> E
    C <--> E
    
    E --> F
    F --> G
    F --> H
    
    G <--> I
    G <--> J
    H <--> K
    G --> L
    
    M --> O
    N --> O
    O <--> E
```

## 🔐 Encryption Key Exchange

```mermaid
sequenceDiagram
    participant D as Desktop
    participant S as Server
    participant M as Mobile

    Note over D,M: Signal Protocol (X3DH) Key Exchange

    D->>D: Generate Identity Key Pair
    D->>D: Generate Signed Pre-Key
    D->>D: Generate One-Time Pre-Keys
    
    D->>S: Upload Public Keys
    Note right of D: Identity Key (public)<br/>Signed Pre-Key (public)<br/>Pre-Key Bundle
    
    M->>S: Request Desktop Keys
    S->>M: Send Key Bundle
    
    M->>M: Generate Ephemeral Key
    M->>M: Perform X3DH
    Note right of M: Calculate shared secret<br/>Derive encryption keys
    
    M->>D: Send Ephemeral Key
    Note right of M: Via server (encrypted)
    
    D->>D: Perform X3DH
    Note right of D: Calculate same shared secret<br/>Derive same encryption keys
    
    Note over D,M: Secure Channel Established
    
    D<->>M: Encrypted Messages
    Note over D,M: AES-256-GCM encryption<br/>Forward secrecy<br/>Message authentication
```

## 📊 Performance Metrics

```mermaid
graph LR
    subgraph "QR Generation"
        A[Request] --> B[Fingerprint: 50ms]
        B --> C[Session: 20ms]
        C --> D[QR Code: 30ms]
        D --> E[Total: ~100ms]
    end
    
    subgraph "Status Polling"
        F[Poll Request] --> G[DB Query: 10ms]
        G --> H[Response: 5ms]
        H --> I[Total: ~15ms]
    end
    
    subgraph "Mobile Scan"
        J[Scan QR] --> K[Parse: 5ms]
        K --> L[API Call: 100ms]
        L --> M[Validation: 20ms]
        M --> N[Total: ~125ms]
    end
```

---

## 🎯 Key Takeaways

### For Developers:
- **Sequence diagrams** show exact API call order
- **State machines** help implement mobile app logic
- **Security flow** ensures proper validation
- **Architecture diagram** shows system components

### For Beginners:
- **Visual flows** make complex processes clear
- **Step-by-step** progression is easy to follow
- **Decision points** show where user input matters
- **Error handling** paths are clearly marked

### For Security Auditors:
- **Validation checkpoints** are clearly identified
- **Encryption flows** follow industry standards
- **Timeout mechanisms** prevent abuse
- **Audit trails** track all activities

Use these diagrams as reference when implementing or debugging the QR authentication system!
