# Status Creation Examples

## Fixed Payload Examples

### 1. Text Status (Working Example)
```json
{
  "content_type": "text",
  "content": "My new status update!",
  "background_color": "#FF5733",
  "text_color": "#FFFFFF",
  "font_style": "bold",
  "privacy_setting": "Contacts",
  "allowed_contacts": [],
  "blocked_contacts": []
}
```

### 2. Text Status with Caption
```json
{
  "content_type": "text",
  "caption": "Having a great day! 😊",
  "background_color": "#4CAF50",
  "text_color": "#FFFFFF",
  "font_style": "italic",
  "privacy_setting": "Everyone",
  "allowed_contacts": [],
  "blocked_contacts": []
}
```

### 3. Text Status with Both Content and Caption
```json
{
  "content_type": "text",
  "content": "Main status content here",
  "caption": "Additional caption text",
  "background_color": "#2196F3",
  "text_color": "#FFFFFF",
  "font_style": "normal",
  "privacy_setting": "ContactsExcept",
  "allowed_contacts": [],
  "blocked_contacts": ["user_123", "user_456"]
}
```

### 4. Image Status (Form Data)
```
POST /api/v1/status/upload
Content-Type: multipart/form-data

content_type: image
caption: Beautiful sunset today!
background_color: #FF9800
privacy_setting: Contacts
allowed_contacts: []
blocked_contacts: []
status_media: <image_file>
```

### 5. Video Status (Form Data)
```
POST /api/v1/status/upload
Content-Type: multipart/form-data

content_type: video
caption: Check out this amazing video!
privacy_setting: OnlyShare
allowed_contacts: ["user_789", "user_101"]
blocked_contacts: []
status_media: <video_file>
```

## Privacy Settings Options

- `"Everyone"` - Visible to all users
- `"Contacts"` - Visible to contacts only
- `"ContactsExcept"` - Visible to contacts except blocked ones
- `"OnlyShare"` - Visible only to specified allowed contacts

## API Endpoints

### Create Text Status
```http
POST /api/v1/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "content_type": "text",
  "content": "Your status text here",
  "background_color": "#FF5733",
  "text_color": "#FFFFFF",
  "font_style": "bold",
  "privacy_setting": "Contacts"
}
```

### Upload Media Status
```http
POST /api/v1/status/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

content_type: image
caption: Your caption here
privacy_setting: Contacts
status_media: <file>
```

### Get All Statuses
```http
GET /api/v1/status
Authorization: Bearer <token>
```

### View Specific Status
```http
POST /api/v1/status/view
Authorization: Bearer <token>
Content-Type: application/json

{
  "status_id": "status_123"
}
```

### Delete Status
```http
DELETE /api/v1/status/status_123
Authorization: Bearer <token>
```

## Common Issues Fixed

1. **Text Status Validation**: Now accepts either `content` or `caption` for text status
2. **Privacy Settings**: Matches the enum values in the model
3. **Error Handling**: Improved error messages and logging
4. **Duplicate Index**: Fixed MongoDB duplicate index warning

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Status created successfully",
  "data": {
    "id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "user_id": "+2347049670618",
    "content_type": "text",
    "content": "My new status update!",
    "background_color": "#FF5733",
    "text_color": "#FFFFFF",
    "font_style": "bold",
    "privacy_setting": "Contacts",
    "view_count": 0,
    "is_active": true,
    "expires_at": "2024-01-16T10:30:00.000Z",
    "created_at": "2024-01-15T10:30:00.000Z"
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Text status requires content or caption",
  "error": {
    "code": "VALIDATION_ERROR",
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```
