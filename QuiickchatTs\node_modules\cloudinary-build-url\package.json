{"name": "cloudinary-build-url", "version": "0.2.4", "description": "Lighter Url generator for Cloudinary", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/cjs/index.d.ts", "sideEffects": false, "directories": {"lib": "lib", "test": "__tests__"}, "files": ["dist"], "scripts": {"build": "npm run clean && npm run compile", "clean": "rm -rf ./dist", "compile": "tsc -p tsconfig.build.json && tsc -p tsconfig.json", "test:unit": "jest __tests__ --reporters default", "tsc": "tsc -p tsconfig.build.json && tsc -p tsconfig.json", "bundlewatch": "bundlewatch --config bundlewatch.config.json", "prepublishOnly": "yarn build && yarn agadoo && yarn bundlewatch", "agadoo": "agadoo dist/", "mkdist": "npx mkdist --src=lib"}, "keywords": ["cloudinary", "cloudinary-url", "url", "video", "image", "optimization", "api", "light", "typescript"], "dependencies": {"@cld-apis/utils": "^0.2.0"}, "devDependencies": {"@cld-apis/types": "^0.1.6", "@types/jest": "^26.0.15", "@types/node": "^14.14.7", "agadoo": "^2.0.0", "bundlewatch": "^0.3.1", "husky": "^4.3.0", "jest": "^26.6.3", "jest-html-reporters": "^2.1.0", "mkdist": "^0.1.1", "ts-jest": "^26.4.4", "typescript": "^4.0.5"}, "bugs": {"url": "https://github.com/mayashavin/cloudinary-api/issues"}, "homepage": "https://github.com/mayashavin/cloudinary-api/tree/main/packages/url#README", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/mayashavin/cloudinary-api.git", "directory": "packages/url"}, "gitHead": "20ec2d90ededb0356d248869fdd66b7e86a8d0b3"}