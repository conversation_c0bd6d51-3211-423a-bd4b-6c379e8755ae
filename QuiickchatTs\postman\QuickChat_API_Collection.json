{"info": {"name": "QuickChat API Collection", "description": "Complete API collection for QuickChat backend with all endpoints, authentication, and real-time features. Updated with comprehensive coverage of all routes and TypeScript interfaces.", "version": "3.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set authorization header if access_token exists", "if (pm.environment.get('access_token')) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + pm.environment.get('access_token')", "    });", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080/api/v1", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success true', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('Response contains phone number', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('phone');", "});", "", "// Store verification code if in development mode", "if (pm.response.json().data.verification_code) {", "    pm.environment.set('verification_code', pm.response.json().data.verification_code);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}, "description": "Register a new user with phone number. In development mode, verification code will be included in response."}}, {"name": "Verify Registration", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains tokens', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('access_token');", "    pm.expect(jsonData.data).to.have.property('refresh_token');", "});", "", "// Store tokens for subsequent requests", "const jsonData = pm.response.json();", "if (jsonData.data.access_token) {", "    pm.environment.set('access_token', jsonData.data.access_token);", "}", "if (jsonData.data.refresh_token) {", "    pm.environment.set('refresh_token', jsonData.data.refresh_token);", "}", "if (jsonData.data.user && jsonData.data.user.phone) {", "    pm.environment.set('user_phone', jsonData.data.user.phone);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\",\n  \"code\": \"{{verification_code}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/verify", "host": ["{{base_url}}"], "path": ["auth", "verify"]}, "description": "Verify registration with SMS code. Returns access and refresh tokens."}}, {"name": "Login User", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Store verification code if in development mode", "if (pm.response.json().data.verification_code) {", "    pm.environment.set('verification_code', pm.response.json().data.verification_code);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Login existing user with phone number. Sends SMS verification code."}}, {"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Store tokens for subsequent requests", "const jsonData = pm.response.json();", "if (jsonData.data.access_token) {", "    pm.environment.set('access_token', jsonData.data.access_token);", "}", "if (jsonData.data.refresh_token) {", "    pm.environment.set('refresh_token', jsonData.data.refresh_token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\",\n  \"code\": \"{{verification_code}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/verify-login", "host": ["{{base_url}}"], "path": ["auth", "verify-login"]}, "description": "Verify login with SMS code. Returns access and refresh tokens."}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Update tokens", "const jsonData = pm.response.json();", "if (jsonData.data.access_token) {", "    pm.environment.set('access_token', jsonData.data.access_token);", "}", "if (jsonData.data.refresh_token) {", "    pm.environment.set('refresh_token', jsonData.data.refresh_token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh-token", "host": ["{{base_url}}"], "path": ["auth", "refresh-token"]}, "description": "Refresh access token using refresh token."}}, {"name": "Resend Verification Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/resend-code", "host": ["{{base_url}}"], "path": ["auth", "resend-code"]}, "description": "Resend verification code to phone number."}}, {"name": "Initialize User", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/init", "host": ["{{base_url}}"], "path": ["auth", "init"]}, "description": "Initialize user session and get basic info."}}], "description": "Authentication endpoints for user registration, login, and token management."}, {"name": "👤 User Management", "item": [{"name": "Get Current User", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('phone');", "});"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/me", "host": ["{{base_url}}"], "path": ["users", "me"]}, "description": "Get current authenticated user's profile information."}}, {"name": "Update Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"john_doe_2024\",\n  \"bio\": \"Software Engineer | Tech Enthusiast | Lagos Based Developer\",\n  \"address\": \"Victoria Island, Lagos, Nigeria\",\n  \"email\": \"<EMAIL>\",\n  \"status\": \"Available for work\",\n  \"device_id\": \"device_12345\",\n  \"registration_id\": \"reg_67890\"\n}"}, "url": {"raw": "{{base_url}}/users/update-profile", "host": ["{{base_url}}"], "path": ["users", "update-profile"]}, "description": "Update user profile information. All fields are optional."}}], "description": "User profile management endpoints."}, {"name": "📱 Status Management", "item": [{"name": "Create Text Status", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains status data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('id');", "    pm.expect(jsonData.data).to.have.property('content_type');", "});", "", "// Store status ID for other requests", "const jsonData = pm.response.json();", "if (jsonData.data.id) {", "    pm.environment.set('test_status_id', jsonData.data.id);", "}"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content_type\": \"text\",\n  \"content\": \"Having an amazing day! 🌟 Just finished a great workout and feeling energized! 💪\",\n  \"background_color\": \"#FF6B6B\",\n  \"text_color\": \"#FFFFFF\",\n  \"font_style\": \"bold\",\n  \"privacy_setting\": \"Contacts\",\n  \"allowed_contacts\": [],\n  \"blocked_contacts\": []\n}"}, "url": {"raw": "{{base_url}}/status", "host": ["{{base_url}}"], "path": ["status"]}, "description": "Create a text-based status update with styling options."}}, {"name": "Upload Media Status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "content_type", "value": "image", "type": "text", "description": "Type: image or video"}, {"key": "caption", "value": "Beautiful sunset from my balcony! 🌅", "type": "text", "description": "Optional caption for media"}, {"key": "privacy_setting", "value": "Contacts", "type": "text", "description": "Privacy: Everyone, Contacts, ContactsExcept, OnlyShare"}, {"key": "allowed_contacts", "value": "[]", "type": "text", "description": "JSON array of allowed contact IDs"}, {"key": "blocked_contacts", "value": "[]", "type": "text", "description": "JSON array of blocked contact IDs"}, {"key": "status_media", "type": "file", "src": [], "description": "Media file (image/video, max 25MB)"}]}, "url": {"raw": "{{base_url}}/status/upload", "host": ["{{base_url}}"], "path": ["status", "upload"]}, "description": "Upload image or video status with optional caption."}}, {"name": "Get Status Feed", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/status/feed", "host": ["{{base_url}}"], "path": ["status", "feed"]}, "description": "Get status updates from contacts (status feed)."}}, {"name": "Get My Statuses", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/status/my", "host": ["{{base_url}}"], "path": ["status", "my"]}, "description": "Get current user's status updates."}}], "description": "Status creation, viewing, and management endpoints."}, {"name": "📞 Contact Management", "item": [{"name": "Upload Contacts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": [\n    {\n      \"display_name\": \"<PERSON>\",\n      \"phone_number\": \"+**********\"\n    },\n    {\n      \"display_name\": \"<PERSON>\",\n      \"phone_number\": \"+1987654321\"\n    },\n    {\n      \"display_name\": \"<PERSON>\",\n      \"phone_number\": \"+1555123456\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/contacts/upload", "host": ["{{base_url}}"], "path": ["contacts", "upload"]}, "description": "Bulk upload contacts from phone book."}}, {"name": "Get All Contacts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/contacts", "host": ["{{base_url}}"], "path": ["contacts"]}, "description": "Get all user contacts."}}, {"name": "Sync Contacts", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": [\n    {\n      \"display_name\": \"Updated Contact\",\n      \"phone_number\": \"+**********\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/contacts/sync", "host": ["{{base_url}}"], "path": ["contacts", "sync"]}, "description": "Sync contacts with server."}}], "description": "Contact management and synchronization endpoints."}, {"name": "🔧 System & Admin", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/health", "host": ["{{base_url}}"], "path": ["system", "health"]}, "description": "Check system health and service status."}}, {"name": "SMS Service Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/sms-status", "host": ["{{base_url}}"], "path": ["system", "sms-status"]}, "description": "Check SMS service configuration and status."}}, {"name": "Test SMS Service", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/system/test-sms", "host": ["{{base_url}}"], "path": ["system", "test-sms"]}, "description": "Test SMS sending functionality with a phone number."}}], "description": "System administration and health check endpoints."}, {"name": "🔐 QR Authentication", "item": [{"name": "Generate QR Code", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// Store session ID for status checking", "const jsonData = pm.response.json();", "if (jsonData.data.sessionId) {", "    pm.environment.set('qr_session_id', jsonData.data.sessionId);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"purpose\": \"login\",\n  \"device_info\": {\n    \"device_name\": \"Chrome Browser\",\n    \"device_type\": \"web\",\n    \"user_agent\": \"Mozilla/5.0...\"\n  }\n}"}, "url": {"raw": "{{base_url}}/qr-auth/generate", "host": ["{{base_url}}"], "path": ["qr-auth", "generate"]}, "description": "Generate QR code for authentication or device linking."}}, {"name": "Check QR Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/qr-auth/status/{{qr_session_id}}", "host": ["{{base_url}}"], "path": ["qr-auth", "status", "{{qr_session_id}}"]}, "description": "Check QR code authentication status."}}, {"name": "Scan QR Code", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"qr_data\": \"qr_session_{{qr_session_id}}\"\n}"}, "url": {"raw": "{{base_url}}/qr-auth/scan", "host": ["{{base_url}}"], "path": ["qr-auth", "scan"]}, "description": "Scan QR code with mobile app."}}], "description": "QR code authentication and device linking endpoints."}]}