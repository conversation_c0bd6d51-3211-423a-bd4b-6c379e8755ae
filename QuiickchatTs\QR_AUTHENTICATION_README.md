# QuickChat QR Authentication & Device Linking Guide

## 📱 Overview

QuickChat uses QR code authentication similar to WhatsApp Web, allowing users to securely link desktop/web clients to their mobile accounts. This guide explains the complete process for developers and beginners.

## 🎯 What is QR Authentication?

QR authentication is a secure method where:
1. **Desktop/Web** generates a QR code
2. **Mobile app** scans the QR code
3. **User approves** the login on mobile
4. **Desktop/Web** gets authenticated access

This is the same process used by WhatsApp Web, Telegram Web, and other messaging platforms.

## 🔄 Authentication Flow Overview

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Desktop   │    │   Server    │    │   Mobile    │
│    Web      │    │             │    │    App      │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │ 1. Generate QR    │                   │
       ├──────────────────►│                   │
       │                   │                   │
       │ 2. QR Code Data   │                   │
       │◄──────────────────┤                   │
       │                   │                   │
       │ 3. Display QR     │                   │
       │                   │                   │
       │ 4. Poll Status    │                   │
       ├──────────────────►│                   │
       │                   │                   │
       │                   │ 5. Scan QR       │
       │                   │◄──────────────────┤
       │                   │                   │
       │                   │ 6. User Approval  │
       │                   │◄──────────────────┤
       │                   │                   │
       │ 7. Login Success  │                   │
       │◄──────────────────┤                   │
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ installed
- MongoDB running (local or cloud)
- QuickChat mobile app (for testing)

### Quick Setup
```bash
# Clone and setup
cd QuiickchatTs
npm install
npm run build

# Start server
npm start

# Open demo
# Visit: http://localhost:8080
```

## 🔧 Technical Implementation

### 1. Device Fingerprinting

Every device gets a unique fingerprint based on:
```javascript
// Browser characteristics
- User Agent
- Screen resolution
- Timezone
- Language
- Hardware specs
- Network interfaces
```

**Purpose**: Prevents unauthorized access and tracks device identity.

### 2. QR Code Generation

**Endpoint**: `POST /api/v1/qr/generate`

**Request**:
```json
{
  "session_type": "login",
  "device_fingerprint": "abc123..."
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "qr_code": "data:image/png;base64,iVBOR...",
    "session_id": "session-xyz789",
    "expires_at": "2024-01-01T12:02:00Z",
    "session_type": "login"
  }
}
```

**QR Code Contains**:
```json
{
  "v": "1.0",           // Version
  "t": "login",         // Type (login/device_link)
  "s": "session-xyz",   // Session ID
  "c": "challenge",     // Security challenge
  "n": "nonce",         // One-time number
  "e": 1704110520       // Expiry timestamp
}
```

### 3. Status Polling

**Endpoint**: `GET /api/v1/qr/status/{sessionId}`

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "pending",  // pending|scanned|approved|rejected|expired
    "expires_at": "2024-01-01T12:02:00Z",
    "session_type": "login"
  }
}
```

**Status Flow**:
- `pending` → Waiting for mobile scan
- `scanned` → Mobile scanned, waiting for approval
- `approved` → User approved → Login success!
- `rejected` → User rejected → Generate new QR
- `expired` → QR expired → Generate new QR

## 📱 Mobile App Integration

### 1. QR Scanning

**Endpoint**: `POST /api/v1/qr/scan`

**Headers**:
```
Authorization: Bearer <mobile_jwt_token>
X-Device-ID: mobile-device-123
```

**Request**:
```json
{
  "qr_data": "{\"v\":\"1.0\",\"t\":\"login\",...}"
}
```

### 2. User Approval

**Endpoint**: `POST /api/v1/qr/approve`

**Request**:
```json
{
  "session_id": "session-xyz789",
  "approved": true
}
```

## 🔗 Device Linking Process

Device linking allows multiple devices (desktop, tablet, etc.) to access the same account.

### 1. Initiate Device Link

**Endpoint**: `POST /api/v1/qr/device/initiate`

**Request**:
```json
{
  "device_name": "John's MacBook",
  "device_type": "desktop",
  "device_fingerprint": "abc123...",
  "identity_key": "base64_key",
  "signed_pre_key": "base64_key",
  "pre_key_bundle": "base64_bundle"
}
```

### 2. Complete Device Link

After mobile approval:
**Endpoint**: `POST /api/v1/qr/device/complete`

**Request**:
```json
{
  "session_id": "session-xyz789",
  "verification_code": "123456"
}
```

## 🛡️ Security Features

### 1. Device Fingerprinting
- **Multi-layer hashing** (SHA-256 + SHA-512)
- **Hardware identifiers** (MAC addresses, CPU info)
- **Browser characteristics** (User-Agent, screen resolution)
- **Network information** (IP addresses, interfaces)

### 2. Session Security
- **Short expiry** (90-120 seconds)
- **One-time use** QR codes
- **Cryptographic challenges** and nonces
- **Rate limiting** (5 requests/minute)

### 3. Encryption
- **Signal Protocol** (X3DH key exchange)
- **Curve25519** elliptic curve
- **AES-256-GCM** message encryption
- **Forward secrecy** with ephemeral keys

## 🎨 Frontend Implementation

### HTML Demo Features
- **Real-time QR generation**
- **Status polling** every 2 seconds
- **Automatic QR rotation** on expiry
- **Loading states** and error handling
- **Demo mode** fallback
- **WhatsApp-like UI**

### Key JavaScript Functions
```javascript
// Generate QR code
generateQRCode()

// Check status
checkQRStatus()

// Handle status updates
handleStatusUpdate(statusData)

// Device fingerprinting
generateDeviceFingerprint()
```

## 🔍 Testing the Demo

### 1. Open Demo Page
```bash
# Start server
npm start

# Visit demo
http://localhost:8080
```

### 2. Demo Mode (No Server)
```bash
# Open HTML file directly
file:///path/to/quickchat-demo.html
```

### 3. Test with Mobile App
1. **Scan QR** with QuickChat mobile app
2. **Approve/Reject** on mobile
3. **Watch status** change in real-time
4. **Login success** → Access granted

## 🐛 Troubleshooting

### Common Issues

**1. CORS Errors**
```bash
# Solution: Use server instead of file://
npm start
# Visit: http://localhost:8080
```

**2. QR Not Generating**
```bash
# Check server logs
npm start
# Check browser console for errors
```

**3. Status Not Updating**
```bash
# Verify polling is working
# Check network tab in browser
# Ensure session ID is valid
```

### Debug Tools

**1. Device Fingerprint Test**
```bash
node scripts/device-fingerprint.js fingerprint
```

**2. QR Generation Test**
```bash
node scripts/device-fingerprint.js qr-login
```

**3. Security Analysis**
```bash
node scripts/device-fingerprint.js security-test
```

## 📊 API Rate Limits

| Endpoint | Limit | Window |
|----------|-------|--------|
| QR Generate | 5 requests | 1 minute |
| Status Check | 30 requests | 1 minute |
| QR Scan | 10 requests | 1 minute |

## 🔄 Session Lifecycle

```
Create → Pending → Scanned → Approved → Consumed
   ↓        ↓        ↓         ↓         ↓
 2 min   2 min    2 min     2 min    Cleanup
```

**Cleanup**: Expired sessions are automatically removed every minute.

## 🎯 Best Practices

### For Developers
1. **Always validate** device fingerprints
2. **Implement rate limiting** on client side
3. **Handle all status states** gracefully
4. **Use HTTPS** in production
5. **Store tokens securely**

### For Users
1. **Scan QR quickly** (2-minute expiry)
2. **Approve only trusted** devices
3. **Revoke unused** device links
4. **Keep mobile app** updated

## 📚 Additional Resources

- **API Documentation**: `API_DOCUMENTATION.md`
- **Device Fingerprint Script**: `scripts/device-fingerprint.js`
- **Demo Page**: `public/quickchat-demo.html`
- **Test Server**: `test-server.js`

## 🔐 Deep Dive: Security Architecture

### Cryptographic Flow

```
1. Device Fingerprint Generation
   ├── Hardware ID (MAC, CPU, Memory)
   ├── Software ID (OS, Browser, Timezone)
   ├── Network ID (IP, Interfaces)
   └── Hash: SHA-256(SHA-512(data + timestamp))

2. QR Code Security
   ├── Session ID: Cryptographically random
   ├── Challenge: Random 32-byte string
   ├── Nonce: One-time 16-byte number
   └── Expiry: Short-lived (90-120 seconds)

3. Mobile Verification
   ├── JWT Token: Validates mobile identity
   ├── Device ID: Links to registered device
   ├── Challenge Response: Proves QR ownership
   └── Approval: User consent required
```

### Attack Prevention

| Attack Type | Prevention Method |
|-------------|------------------|
| **Replay Attacks** | One-time nonces + timestamps |
| **Session Hijacking** | Short expiry + device binding |
| **Fingerprint Spoofing** | Multi-layer validation |
| **Rate Limit Bypass** | IP + fingerprint tracking |
| **Man-in-the-Middle** | HTTPS + certificate pinning |

## 🏗️ Architecture Patterns

### 1. Observer Pattern (Status Polling)
```javascript
// Frontend polls server every 2 seconds
setInterval(async () => {
  const status = await checkQRStatus(sessionId);
  handleStatusUpdate(status);
}, 2000);
```

### 2. State Machine (QR Lifecycle)
```
States: [pending, scanned, approved, rejected, expired, consumed]
Transitions: pending → scanned → approved → consumed
```

### 3. Factory Pattern (QR Generation)
```javascript
class QRFactory {
  static create(type, fingerprint) {
    switch(type) {
      case 'login': return new LoginQR(fingerprint);
      case 'device_link': return new DeviceLinkQR(fingerprint);
    }
  }
}
```

## 📱 Mobile App Integration Guide

### Step 1: QR Scanner Setup
```javascript
// React Native example
import { RNCamera } from 'react-native-camera';

const QRScanner = () => {
  const onBarCodeRead = async (data) => {
    try {
      const qrData = JSON.parse(data.data);
      await scanQRCode(qrData);
    } catch (error) {
      showError('Invalid QR code');
    }
  };

  return (
    <RNCamera
      onBarCodeRead={onBarCodeRead}
      barCodeTypes={[RNCamera.Constants.BarCodeType.qr]}
    />
  );
};
```

### Step 2: API Integration
```javascript
// Scan QR code
const scanQRCode = async (qrData) => {
  const response = await fetch('/api/v1/qr/scan', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'X-Device-ID': deviceId,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ qr_data: JSON.stringify(qrData) })
  });

  const result = await response.json();
  if (result.success) {
    showApprovalDialog(result.data);
  }
};

// Approve/Reject
const approveLogin = async (sessionId, approved) => {
  await fetch('/api/v1/qr/approve', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      session_id: sessionId,
      approved
    })
  });
};
```

## 🧪 Testing Scenarios

### 1. Happy Path Testing
```bash
# Test complete flow
1. Generate QR → ✅ Success
2. Scan with mobile → ✅ Status: scanned
3. Approve on mobile → ✅ Status: approved
4. Desktop login → ✅ Success
```

### 2. Error Scenarios
```bash
# Test error handling
1. Expired QR → ❌ Auto-regenerate
2. Invalid QR → ❌ Show error
3. Network failure → ❌ Retry mechanism
4. User rejection → ❌ Generate new QR
```

### 3. Security Testing
```bash
# Test security measures
1. Rate limiting → ❌ Block after 5 requests
2. Replay attack → ❌ Nonce validation fails
3. Expired session → ❌ Session not found
4. Invalid fingerprint → ❌ Validation fails
```

## 🔧 Configuration Options

### Environment Variables
```bash
# Server Configuration
PORT=8080
HOST=0.0.0.0
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/quickchat
MONGODB_DATABASE=QuiickchatDBhQ

# Security
JWT_SECRET=your_secret_here
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# QR Settings
QR_EXPIRY_MINUTES=2
QR_RATE_LIMIT=5
QR_MAX_SIZE=2048
```

### Customization
```javascript
// Adjust polling interval
const POLLING_INTERVAL = 2000; // 2 seconds

// Customize QR expiry
const QR_EXPIRY = 120; // 2 minutes

// Rate limiting
const RATE_LIMIT = {
  windowMs: 60000, // 1 minute
  max: 5 // 5 requests per window
};
```

## 🤝 Contributing

### Development Setup
```bash
# 1. Clone repository
git clone <repository-url>
cd QuiickchatTs

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env
# Edit .env with your settings

# 4. Start development
npm run dev
```

### Testing Guidelines
```bash
# Run all tests
npm test

# Test QR authentication
npm run test:qr

# Test device fingerprinting
npm run test:fingerprint

# Test security features
npm run test:security
```

### Code Style
- Use TypeScript for type safety
- Follow ESLint configuration
- Write comprehensive tests
- Document API changes
- Test on multiple devices

---

## 📞 Support & Resources

**Documentation**:
- API Docs: `API_DOCUMENTATION.md`
- Security Guide: `SECURITY.md`
- Deployment Guide: `DEPLOYMENT.md`

**Testing Tools**:
- Demo Page: `http://localhost:8080`
- Device Fingerprint: `npm run test:fingerprint`
- QR Generator: `npm run test:qr-login`

**Need Help?**
- Check browser console for errors
- Review server logs for API issues
- Test with device fingerprint script
- Use demo mode for offline testing

**Security Concerns?**
- Review security analysis: `npm run test:security`
- Check rate limiting configuration
- Verify HTTPS in production
- Audit device fingerprinting
