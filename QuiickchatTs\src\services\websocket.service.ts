import { FastifyInstance } from 'fastify';
import { Auth } from '../middleware/auth.middleware';
import { logger } from '../utils/logger';
import { AuthenticatedRequest } from '../types';

interface ConnectedClient {
  socket: any;
  phone: string;
  subscriptions: Set<string>;
}

class WebSocketManager {
  private clients: Map<string, ConnectedClient> = new Map();
  private phoneToClientId: Map<string, string> = new Map();

  addClient(clientId: string, socket: any, phone: string): void {
    const client: ConnectedClient = {
      socket,
      phone,
      subscriptions: new Set()
    };

    this.clients.set(clientId, client);
    this.phoneToClientId.set(phone, clientId);
    
    logger.info(`Client connected: ${phone} (${clientId})`);
  }

  removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      this.phoneToClientId.delete(client.phone);
      this.clients.delete(clientId);
      logger.info(`Client disconnected: ${client.phone} (${clientId})`);
    }
  }

  subscribeToContact(clientId: string, contactPhone: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.add(contactPhone);
      logger.info(`${client.phone} subscribed to ${contactPhone}`);
    }
  }

  unsubscribeFromContact(clientId: string, contactPhone: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.delete(contactPhone);
      logger.info(`${client.phone} unsubscribed from ${contactPhone}`);
    }
  }

  async broadcastStatusUpdate(userPhone: string, statusData: any): Promise<void> {
    try {
      // Get user's contacts to determine who should receive the update
      const { User } = await import('../models/user.model');
      const user = await User.findOne({ phone: userPhone });

      if (!user) {
        logger.warn(`User not found for status broadcast: ${userPhone}`);
        return;
      }

      // Get all contacts of the user
      const userContacts = user.contacts || [];

      // Filter clients based on privacy settings and contact relationships
      const eligibleClients = Array.from(this.clients.values()).filter(client => {
        // Check if the client is in the user's contacts
        const isContact = userContacts.includes(client.phone);

        // Apply privacy filtering
        switch (statusData.privacy_setting) {
          case 'Everyone':
            return true;
          case 'Contacts':
            return isContact;
          case 'ContactsExcept':
            return isContact && !statusData.blocked_contacts?.includes(client.phone);
          case 'OnlyShare':
            return statusData.allowed_contacts?.includes(client.phone);
          default:
            return isContact;
        }
      });

      const message = JSON.stringify({
        type: 'status_update',
        user_phone: userPhone,
        data: {
          ...statusData,
          // Add metadata for intelligent sorting
          priority: this.calculateStatusPriority(statusData),
          interaction_score: await this.getInteractionScore(userPhone, statusData),
          is_recent: this.isRecentStatus(statusData.created_at)
        },
        timestamp: new Date().toISOString()
      });

      // Send to eligible clients
      eligibleClients.forEach(client => {
        try {
          client.socket.send(message);
          logger.info(`Status update sent to ${client.phone} for ${userPhone}`);
        } catch (error) {
          logger.error(`Failed to send status update to ${client.phone}:`, error);
          const clientId = this.getClientIdByPhone(client.phone);
          if (clientId) {
            this.removeClient(clientId);
          }
        }
      });

      logger.info(`Status broadcast completed: ${eligibleClients.length} recipients for ${userPhone}`);
    } catch (error) {
      logger.error('Error in broadcastStatusUpdate:', error);
    }
  }

  private calculateStatusPriority(statusData: any): number {
    let priority = 0;

    // Higher priority for recent statuses
    const hoursOld = (Date.now() - new Date(statusData.created_at).getTime()) / (1000 * 60 * 60);
    if (hoursOld < 1) priority += 10;
    else if (hoursOld < 6) priority += 5;
    else if (hoursOld < 12) priority += 2;

    // Higher priority for media content
    if (statusData.content_type === 'video') priority += 3;
    else if (statusData.content_type === 'image') priority += 2;

    // Higher priority for statuses with high view counts
    priority += Math.min(statusData.view_count * 0.1, 5);

    return priority;
  }

  private async getInteractionScore(userPhone: string, statusData: any): Promise<number> {
    try {
      // This would typically query a database for interaction history
      // For now, return a base score
      return 1.0;
    } catch (error) {
      return 1.0;
    }
  }

  private isRecentStatus(createdAt: string | Date | undefined): boolean {
    if (!createdAt) return false;

    const statusTime = new Date(createdAt).getTime();
    const now = Date.now();
    const sixHoursInMs = 6 * 60 * 60 * 1000;

    return (now - statusTime) < sixHoursInMs;
  }

  private getClientIdByPhone(phone: string): string | null {
    for (const [clientId, client] of this.clients.entries()) {
      if (client.phone === phone) {
        return clientId;
      }
    }
    return null;
  }





  getClientCount(): number {
    return this.clients.size;
  }

  getSubscriptionCount(userPhone: string): number {
    return Array.from(this.clients.values()).filter(
      client => client.subscriptions.has(userPhone)
    ).length;
  }

  sendQRStatusUpdate(sessionId: string, status: string, data?: any): void {
    const message = {
      type: 'qr_status_update',
      session_id: sessionId,
      status,
      data,
      timestamp: new Date().toISOString()
    };

    this.broadcast(JSON.stringify(message));
    logger.info(`QR status update sent for session ${sessionId}: ${status}`);
  }

  sendDeviceLinkUpdate(userPhone: string, deviceId: string, status: string, data?: any): void {
    const message = {
      type: 'device_link_update',
      device_id: deviceId,
      status,
      data,
      timestamp: new Date().toISOString()
    };

    this.sendToUser(userPhone, JSON.stringify(message));
    logger.info(`Device link update sent to ${userPhone} for device ${deviceId}: ${status}`);
  }

  sendSecurityAlert(userPhone: string, alertType: string, data: any): void {
    const message = {
      type: 'security_alert',
      alert_type: alertType,
      data,
      timestamp: new Date().toISOString()
    };

    this.sendToUser(userPhone, JSON.stringify(message));
    logger.info(`Security alert sent to ${userPhone}: ${alertType}`);
  }

  sendToUserDevices(userPhone: string, message: string): void {
    this.sendToUser(userPhone, message);
  }

  subscribeToQRSession(clientId: string, sessionId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.add(`qr_${sessionId}`);
      logger.info(`Client ${clientId} subscribed to QR session ${sessionId}`);
    }
  }

  unsubscribeFromQRSession(clientId: string, sessionId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.delete(`qr_${sessionId}`);
      logger.info(`Client ${clientId} unsubscribed from QR session ${sessionId}`);
    }
  }

  sendToQRSubscribers(sessionId: string, message: string): void {
    const subscriptionKey = `qr_${sessionId}`;

    for (const [clientId, client] of this.clients.entries()) {
      if (client.subscriptions.has(subscriptionKey)) {
        try {
          client.socket.send(message);
        } catch (error) {
          logger.error(`Failed to send message to QR subscriber ${clientId}:`, error);
          this.removeClient(clientId);
        }
      }
    }
  }

  getClientPhone(clientId: string): string | null {
    const client = this.clients.get(clientId);
    return client ? client.phone : null;
  }

  broadcast(message: string): void {
    for (const [clientId, client] of this.clients.entries()) {
      try {
        client.socket.send(message);
      } catch (error) {
        logger.error(`Failed to broadcast message to client ${clientId}:`, error);
        this.removeClient(clientId);
      }
    }
  }

  sendToUser(userPhone: string, message: string): void {
    for (const [clientId, client] of this.clients.entries()) {
      if (client.subscriptions.has(userPhone)) {
        try {
          client.socket.send(message);
        } catch (error) {
          logger.error(`Failed to send message to user ${userPhone}, client ${clientId}:`, error);
          this.removeClient(clientId);
        }
      }
    }
  }

  async getRecentStatusUpdates(userPhone: string): Promise<any[]> {
    try {
      const { User } = await import('../models/user.model');
      const { Status } = await import('../models/status.model');

      const user = await User.findOne({ phone: userPhone });
      if (!user || !user.contacts) return [];

      // Get recent statuses from contacts (last 24 hours)
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const recentStatuses = await Status.find({
        user_id: { $in: user.contacts },
        is_active: true,
        expires_at: { $gt: new Date() },
        createdAt: { $gte: twentyFourHoursAgo }
      })
      .sort({ createdAt: -1 })
      .limit(50)
      .lean();

      // Add intelligent sorting metadata
      return recentStatuses.map(status => ({
        ...status,
        priority: this.calculateStatusPriority(status),
        is_recent: this.isRecentStatus(status.createdAt),
        id: status._id.toString()
      }));
    } catch (error) {
      logger.error('Error getting recent status updates:', error);
      return [];
    }
  }

  async getContactStatusSummaries(userPhone: string): Promise<any[]> {
    try {
      const { User } = await import('../models/user.model');
      const { Status } = await import('../models/status.model');

      const user = await User.findOne({ phone: userPhone });
      if (!user || !user.contacts) return [];

      const summaries = [];

      for (const contactPhone of user.contacts) {
        const contactUser = await User.findOne({ phone: contactPhone });
        if (!contactUser) continue;

        const activeStatuses = await Status.find({
          user_id: contactPhone,
          is_active: true,
          expires_at: { $gt: new Date() }
        }).sort({ createdAt: -1 });

        if (activeStatuses.length > 0) {
          const latestStatus = activeStatuses[0];
          if (latestStatus) {
            const unviewedCount = activeStatuses.filter(status =>
              !status.viewers?.includes(userPhone)
            ).length;

            summaries.push({
              user_phone: contactPhone,
              username: contactUser.username,
              profile_picture: contactUser.profile_picture,
              total_statuses: activeStatuses.length,
              unviewed_statuses: unviewedCount,
              latest_status_time: latestStatus.createdAt || new Date(),
              latest_status_type: latestStatus.content_type || 'text',
              has_recent_activity: this.isRecentStatus(latestStatus.createdAt)
            });
          }
        }
      }

      // Sort by priority: unviewed count, recent activity, then latest status time
      return summaries.sort((a, b) => {
        if (a.unviewed_statuses !== b.unviewed_statuses) {
          return b.unviewed_statuses - a.unviewed_statuses;
        }
        if (a.has_recent_activity !== b.has_recent_activity) {
          return a.has_recent_activity ? -1 : 1;
        }
        const timeA = a.latest_status_time ? new Date(a.latest_status_time).getTime() : 0;
        const timeB = b.latest_status_time ? new Date(b.latest_status_time).getTime() : 0;
        return timeB - timeA;
      });
    } catch (error) {
      logger.error('Error getting contact status summaries:', error);
      return [];
    }
  }

  async trackStatusInteraction(userPhone: string, statusId: string, interactionType: string, ownerPhone: string): Promise<void> {
    try {
      // This could be expanded to track interactions in a database
      // For now, just log the interaction
      logger.info(`Status interaction: ${userPhone} ${interactionType} status ${statusId} from ${ownerPhone}`);

      // You could implement interaction scoring here for future intelligent sorting
      // Example: store in Redis or database for ML-based recommendations
    } catch (error) {
      logger.error('Error tracking status interaction:', error);
    }
  }

  async broadcastStatusView(statusId: string, viewerPhone: string, ownerPhone: string): Promise<void> {
    try {
      // Broadcast to the status owner that someone viewed their status
      const ownerClients = Array.from(this.clients.values()).filter(
        client => client.phone === ownerPhone
      );

      const viewMessage = JSON.stringify({
        type: 'status_viewed',
        status_id: statusId,
        viewer_phone: viewerPhone,
        timestamp: new Date().toISOString()
      });

      ownerClients.forEach(client => {
        try {
          client.socket.send(viewMessage);
          logger.info(`Status view notification sent to owner ${ownerPhone}`);
        } catch (error) {
          logger.error(`Failed to send view notification to ${ownerPhone}:`, error);
          const clientId = this.getClientIdByPhone(ownerPhone);
          if (clientId) {
            this.removeClient(clientId);
          }
        }
      });

      // Also broadcast to contacts that the status was viewed (for view count updates)
      const { User } = await import('../models/user.model');
      const owner = await User.findOne({ phone: ownerPhone });

      if (owner && owner.contacts) {
        const contactClients = Array.from(this.clients.values()).filter(
          client => owner.contacts?.includes(client.phone)
        );

        const updateMessage = JSON.stringify({
          type: 'status_view_count_updated',
          status_id: statusId,
          owner_phone: ownerPhone,
          timestamp: new Date().toISOString()
        });

        contactClients.forEach(client => {
          try {
            client.socket.send(updateMessage);
          } catch (error) {
            logger.error(`Failed to send view count update to ${client.phone}:`, error);
          }
        });
      }
    } catch (error) {
      logger.error('Error broadcasting status view:', error);
    }
  }

  async broadcastStatusDeletion(statusId: string, ownerPhone: string): Promise<void> {
    try {
      const { User } = await import('../models/user.model');
      const owner = await User.findOne({ phone: ownerPhone });

      if (!owner || !owner.contacts) return;

      // Broadcast to all contacts that the status was deleted
      const contactClients = Array.from(this.clients.values()).filter(
        client => owner.contacts?.includes(client.phone)
      );

      const deleteMessage = JSON.stringify({
        type: 'status_deleted',
        status_id: statusId,
        owner_phone: ownerPhone,
        timestamp: new Date().toISOString()
      });

      contactClients.forEach(client => {
        try {
          client.socket.send(deleteMessage);
          logger.info(`Status deletion notification sent to ${client.phone}`);
        } catch (error) {
          logger.error(`Failed to send deletion notification to ${client.phone}:`, error);
          const clientId = this.getClientIdByPhone(client.phone);
          if (clientId) {
            this.removeClient(clientId);
          }
        }
      });

      logger.info(`Status deletion broadcast completed: ${contactClients.length} recipients for ${ownerPhone}`);
    } catch (error) {
      logger.error('Error broadcasting status deletion:', error);
    }
  }
}

export const wsManager = new WebSocketManager();

export const setupWebSocket = async (fastify: FastifyInstance): Promise<void> => {
  await fastify.register(require('@fastify/websocket'));

  fastify.register(async function (fastify) {
    (fastify as any).get('/status-updates', { websocket: true }, (connection: any, _req: any) => {
      const clientId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      connection.socket.on('message', async (message: any) => {
        try {
          const data = JSON.parse(message.toString());
          
          if (data.type === 'auth') {
            const token = data.token;
            if (!token) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Authentication token required'
              }));
              connection.socket.close();
              return;
            }

            try {
              const claims = Auth.validateAccessToken(token);
              wsManager.addClient(clientId, connection.socket, claims.phone);
              
              connection.socket.send(JSON.stringify({
                type: 'auth_success',
                message: 'Connected successfully'
              }));
            } catch (error) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Invalid authentication token'
              }));
              connection.socket.close();
              return;
            }
          }

          if (data.type === 'subscribe') {
            const contactPhone = data.contact_phone;
            if (contactPhone) {
              wsManager.subscribeToContact(clientId, contactPhone);
              connection.socket.send(JSON.stringify({
                type: 'subscribed',
                contact_phone: contactPhone
              }));
            }
          }

          if (data.type === 'unsubscribe') {
            const contactPhone = data.contact_phone;
            if (contactPhone) {
              wsManager.unsubscribeFromContact(clientId, contactPhone);
              connection.socket.send(JSON.stringify({
                type: 'unsubscribed',
                contact_phone: contactPhone
              }));
            }
          }

          if (data.type === 'ping') {
            connection.socket.send(JSON.stringify({
              type: 'pong',
              timestamp: new Date().toISOString()
            }));
          }

          // Status management messages
          if (data.type === 'request_status_updates') {
            // Send recent status updates for contacts
            try {
              const userPhone = wsManager.getClientPhone(clientId);
              if (userPhone) {
                const recentStatuses = await wsManager.getRecentStatusUpdates(userPhone);
                connection.socket.send(JSON.stringify({
                  type: 'status_updates_batch',
                  statuses: recentStatuses,
                  timestamp: new Date().toISOString()
                }));
              }
            } catch (error) {
              logger.error('Error fetching recent status updates:', error);
            }
          }

          if (data.type === 'mark_status_viewed') {
            const { status_id, owner_phone } = data;
            if (status_id && owner_phone) {
              const viewerPhone = wsManager.getClientPhone(clientId);
              if (viewerPhone) {
                wsManager.broadcastStatusView(status_id, viewerPhone, owner_phone);
              }
            }
          }

          if (data.type === 'request_contact_statuses') {
            // Send status summaries for all contacts
            try {
              const userPhone = wsManager.getClientPhone(clientId);
              if (userPhone) {
                const contactStatuses = await wsManager.getContactStatusSummaries(userPhone);
                connection.socket.send(JSON.stringify({
                  type: 'contact_statuses',
                  summaries: contactStatuses,
                  timestamp: new Date().toISOString()
                }));
              }
            } catch (error) {
              logger.error('Error fetching contact statuses:', error);
            }
          }

          if (data.type === 'status_interaction') {
            // Track status interactions for intelligent sorting
            const { status_id, interaction_type, owner_phone } = data;
            if (status_id && interaction_type && owner_phone) {
              const userPhone = wsManager.getClientPhone(clientId);
              if (userPhone) {
                await wsManager.trackStatusInteraction(userPhone, status_id, interaction_type, owner_phone);
              }
            }
          }

          if (data.type === 'subscribe_qr' && data.sessionId) {
            wsManager.subscribeToQRSession(clientId, data.sessionId);
            connection.socket.send(JSON.stringify({
              type: 'qr_subscribed',
              sessionId: data.sessionId
            }));
          }

          if (data.type === 'unsubscribe_qr' && data.sessionId) {
            wsManager.unsubscribeFromQRSession(clientId, data.sessionId);
            connection.socket.send(JSON.stringify({
              type: 'qr_unsubscribed',
              sessionId: data.sessionId
            }));
          }

          if (data.type === 'qr_status_check' && data.sessionId) {
            try {
              const qrAuthService = await import('./qr-auth.service');
              const status = await qrAuthService.getSessionStatus(data.sessionId);

              connection.socket.send(JSON.stringify({
                type: 'qr_status_response',
                sessionId: data.sessionId,
                status: status.status,
                expiresAt: status.expiresAt
              }));
            } catch (error) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Failed to check QR status'
              }));
            }
          }

          if (data.type === 'device_sync' && data.payload) {
            try {
              const encryptedSyncService = await import('./encrypted-sync.service');
              await encryptedSyncService.EncryptedSyncService.receiveSyncMessage(
                wsManager.getClientPhone(clientId) || '',
                data.payload
              );

              connection.socket.send(JSON.stringify({
                type: 'sync_received',
                messageId: data.payload.messageId
              }));
            } catch (error) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Failed to process sync message'
              }));
            }
          }

        } catch (error) {
          logger.error('WebSocket message error:', error);
          connection.socket.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });

      connection.socket.on('close', () => {
        wsManager.removeClient(clientId);
      });

      connection.socket.on('error', (error: any) => {
        logger.error('WebSocket error:', error);
        wsManager.removeClient(clientId);
      });
    });
  });

  logger.info('WebSocket service initialized');
};
