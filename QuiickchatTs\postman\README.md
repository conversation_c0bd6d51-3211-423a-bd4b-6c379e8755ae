# QuickChat API Postman Collection v3.0

## 📋 Overview

This comprehensive Postman collection covers all QuickChat API endpoints with proper authentication, request validation, and automated testing. The collection is synchronized with the TypeScript interfaces and route definitions.

## 📁 Collection Structure

### 🔐 Authentication
- **Register User** - Create new user account with phone verification
- **Verify Registration** - Confirm registration with SMS code
- **Login User** - Login existing user
- **Verify Login** - Confirm login with SMS code  
- **Refresh Token** - Refresh expired access tokens
- **Resend Verification Code** - Resend SMS verification
- **Initialize User** - Get basic user session info

### 👤 User Management
- **Get Current User** - Retrieve authenticated user profile
- **Update Profile** - Update user information (username, bio, etc.)
- **Upload Profile Picture** - Upload profile image (JPG/PNG, max 5MB)
- **Delete Account** - Permanently delete user account

### 📱 Status Management
- **Create Text Status** - Create text-based status with styling
- **Upload Media Status** - Upload image/video status (max 25MB)
- **Get Status Feed** - Retrieve status updates from contacts
- **Get My Statuses** - Get current user's status updates

### 📞 Contact Management
- **Upload Contacts** - Bulk upload phone book contacts
- **Get All Contacts** - Retrieve user's contact list
- **Sync Contacts** - Synchronize contacts with server

### 🔧 System & Admin
- **Health Check** - System health and service status
- **SMS Service Status** - Check SMS configuration
- **Test SMS Service** - Test SMS sending functionality

### 🔐 QR Authentication
- **Generate QR Code** - Create QR for device linking
- **Check QR Status** - Monitor QR authentication status
- **Scan QR Code** - Process QR scan from mobile app

## 🚀 Quick Start

### 1. Import Collection & Environment

1. **Import Collection**: 
   - Open Postman
   - Click "Import" → Select `QuickChat_API_Collection.json`

2. **Import Environment**:
   - Click gear icon (⚙️) → "Import" → Select `QuickChat_Environment.json`
   - Select "QuickChat Environment v3.0" from dropdown

### 2. Configure Environment Variables

Update these key variables in your environment:

```json
{
  "base_url": "http://localhost:8080/api/v1",
  "phone_number": "+2347049670618",
  "verification_code": "123456"
}
```

### 3. Authentication Flow

1. **Register/Login**: Use your phone number
2. **Verify**: Use SMS code (auto-filled in development mode)
3. **Tokens**: Access and refresh tokens are automatically stored

## 🔧 Environment Variables

### Auto-Set Variables
These are automatically set by test scripts:
- `access_token` - JWT access token
- `refresh_token` - JWT refresh token  
- `user_phone` - Authenticated user's phone
- `verification_code` - SMS code (development mode)
- `test_status_id` - Created status ID
- `qr_session_id` - QR authentication session

### Manual Variables
Update these as needed:
- `base_url` - API endpoint URL
- `phone_number` - Your test phone number
- `test_contact_phone` - Contact for testing

## 📝 Request Body Examples

### Text Status Creation
```json
{
  "content_type": "text",
  "content": "Having an amazing day! 🌟",
  "background_color": "#FF6B6B",
  "text_color": "#FFFFFF", 
  "font_style": "bold",
  "privacy_setting": "Contacts",
  "allowed_contacts": [],
  "blocked_contacts": []
}
```

### Profile Update
```json
{
  "username": "john_doe_2024",
  "bio": "Software Engineer | Tech Enthusiast",
  "address": "Victoria Island, Lagos, Nigeria",
  "email": "<EMAIL>",
  "status": "Available for work"
}
```

### Contact Upload
```json
{
  "contacts": [
    {
      "display_name": "John Smith",
      "phone_number": "+1234567890"
    },
    {
      "display_name": "Jane Doe", 
      "phone_number": "+1987654321"
    }
  ]
}
```

## 🧪 Automated Testing

### Test Scripts Included

Each request includes test scripts that:
- ✅ Validate HTTP status codes
- ✅ Check response structure
- ✅ Extract and store tokens/IDs
- ✅ Verify required fields

### Example Test Output
```
✅ Status code is 201
✅ Response has success true  
✅ Response contains phone number
✅ Access token stored in environment
```

## 🔒 Authentication

### Bearer Token
All protected endpoints use:
```
Authorization: Bearer {{access_token}}
```

### Token Management
- **Access tokens** expire in 15 minutes
- **Refresh tokens** expire in 7 days
- Use "Refresh Token" request when access token expires

## 📤 File Uploads

### Profile Picture
- **Format**: JPG, PNG
- **Size**: Max 5MB
- **Content-Type**: `multipart/form-data`

### Status Media
- **Images**: JPG, PNG (max 10MB)
- **Videos**: MP4, MOV (max 25MB, 30 seconds)
- **Content-Type**: `multipart/form-data`

## 🌐 Environment Switching

### Development
```json
{
  "base_url": "http://localhost:8080/api/v1",
  "websocket_url": "ws://localhost:8080/status-updates"
}
```

### Production
```json
{
  "base_url": "https://your-domain.com/api/v1", 
  "websocket_url": "wss://your-domain.com/status-updates"
}
```

## 🐛 Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check if access token is set
   - Try refreshing token
   - Re-authenticate if needed

2. **SMS Not Received**
   - Verify phone number format (+1234567890)
   - Check Twilio configuration
   - Use development mode for testing

3. **File Upload Fails**
   - Check file size limits
   - Verify file format
   - Ensure multipart/form-data content type

4. **Environment Variables Missing**
   - Ensure environment is selected
   - Check variable names match exactly
   - Re-import environment if needed

### Debug Tips

- Check **Console** tab for test script output
- Use **Network** tab to inspect raw requests
- Enable **Request/Response logging** in settings
- Verify **Headers** are correctly set

## 📊 Collection Statistics

- **Total Requests**: 20+
- **Folders**: 6 organized sections
- **Test Scripts**: Automated validation
- **Environment Variables**: 16 pre-configured
- **Coverage**: 100% of API endpoints

## 🔄 Updates & Versioning

### Version 3.0 Features
- ✅ Complete endpoint coverage
- ✅ TypeScript interface alignment
- ✅ Automated token management
- ✅ Comprehensive test scripts
- ✅ QR authentication support
- ✅ Media upload examples
- ✅ Environment variable automation

### Staying Updated
- Re-import collection when API changes
- Check for new environment variables
- Update base URLs for different environments
- Sync with latest TypeScript interfaces

This collection provides a complete testing suite for the QuickChat API with production-ready examples and automated workflows.
