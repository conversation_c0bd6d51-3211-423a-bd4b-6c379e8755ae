import { FastifyRequest } from 'fastify';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface UpdateProfileRequest {
  username?: string;
  bio?: string;
  address?: string;
  email?: string;
  status?: string;
  device_id?: string;
  registration_id?: string;
}

export interface UploadProfilePictureRequest {
  profilePicture: File | Buffer;
}

export interface UpdateStatusRequest {
  status: string;
}

export interface UpdateLocationRequest {
  address: string;
  latitude?: number;
  longitude?: number;
}

export interface BlockUserRequest {
  user_id: string;
  reason?: string;
}

export interface UnblockUserRequest {
  user_id: string;
}

export interface AddContactRequest {
  phone_number: string;
  display_name?: string;
}

export interface RemoveContactRequest {
  contact_id: string;
}

export interface UpdateContactRequest {
  contact_id: string;
  display_name?: string;
  notes?: string;
}

export interface SearchUsersRequest {
  query: string;
  type?: 'username' | 'phone' | 'email' | 'all';
  limit?: number;
}

export interface CallRequest {
  contact_id: string;
  call_type: 'audio' | 'video';
}

export interface EndCallRequest {
  call_id: string;
  duration_seconds: number;
  status: 'completed' | 'missed' | 'declined';
}

export interface SendMessageRequest {
  contact_id: string;
  message: string;
  message_type?: 'text' | 'image' | 'video' | 'audio' | 'document';
  media_url?: string;
}

export interface MarkMessageReadRequest {
  contact_id: string;
  message_ids: string[];
}

export interface DeleteMessageRequest {
  message_id: string;
  delete_for_everyone?: boolean;
}

export interface CreateGroupRequest {
  name: string;
  description?: string;
  participants: string[];
  group_picture?: string;
}

export interface UpdateGroupRequest {
  group_id: string;
  name?: string;
  description?: string;
  group_picture?: string;
}

export interface AddGroupMembersRequest {
  group_id: string;
  user_ids: string[];
}

export interface RemoveGroupMembersRequest {
  group_id: string;
  user_ids: string[];
}

export interface LeaveGroupRequest {
  group_id: string;
}

export interface UpdateDeviceInfoRequest {
  device_id: string;
  device_name?: string;
  device_type?: 'ios' | 'android' | 'web' | 'desktop';
  app_version?: string;
  os_version?: string;
  push_token?: string;
}

export interface UpdateEncryptionKeysRequest {
  identity_key?: string;
  signed_pre_key?: string;
  pre_key?: string;
  one_time_keys?: string[];
}

export interface ReportUserRequest {
  reported_user_id: string;
  reason: 'spam' | 'harassment' | 'inappropriate_content' | 'fake_account' | 'other';
  description?: string;
  evidence_urls?: string[];
}

export interface FeedbackRequest {
  type: 'bug' | 'feature_request' | 'general';
  title: string;
  description: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high';
  attachments?: string[];
}

export interface ChangePhoneRequest {
  new_phone: string;
  verification_code: string;
}

export interface DeleteAccountRequest {
  confirmation_text: string;
  reason?: string;
}

export interface ExportDataRequest {
  data_types: ('profile' | 'contacts' | 'messages' | 'calls' | 'media')[];
  format?: 'json' | 'csv';
}

export interface NotificationSettingsRequest {
  message_notifications?: boolean;
  call_notifications?: boolean;
  group_notifications?: boolean;
  status_notifications?: boolean;
  sound_enabled?: boolean;
  vibration_enabled?: boolean;
  notification_tone?: string;
}

export interface PrivacySettingsRequest {
  profile_photo_visibility?: 'everyone' | 'contacts' | 'nobody';
  last_seen_visibility?: 'everyone' | 'contacts' | 'nobody';
  status_visibility?: 'everyone' | 'contacts' | 'nobody';
  read_receipts_enabled?: boolean;
  typing_indicators_enabled?: boolean;
}

export interface BackupRequest {
  include_media?: boolean;
  backup_password?: string;
}

export interface RestoreBackupRequest {
  backup_file_url: string;
  backup_password?: string;
}

export interface UserQuery {
  phone?: string;
  user_id?: string;
}

export type AuthenticatedRequest = FastifyRequest & {
  user?: {
    phone: string;
    userId?: string;
    roles?: string[];
  };
};

export interface PaginationQuery {
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface RegisterRequest {
  phone: string;
}

export interface VerifyRequest {
  phone: string;
  code: string;
}

export interface LoginRequest {
  phone: string;
}

export interface VerifyLoginRequest {
  phone: string;
  code: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface JWTClaims {
  phone: string;
  exp: number;
  iat: number;
  jti: string;
  token_type: 'access' | 'refresh';
  roles?: string[];
}

export interface DatabaseConfig {
  uri: string;
  dbName: string;
}

export interface TwilioConfig {
  accountSid: string;
  authToken: string;
  fromNumber: string;
  enabled: boolean;
}

export interface CloudinaryConfig {
  cloudName: string;
  apiKey: string;
  apiSecret: string;
}

export interface ZengoConfig {
  appId: string;
  serverSecret: string;
  region: 'sha' | 'hkg' | 'fra' | 'lax' | 'bom' | 'sgp' | 'unified';
  enabled: boolean;
}

export interface ContactInput {
  display_name: string;
  phone_number: string;
}

export interface BulkContactsRequest {
  contacts: ContactInput[];
}

export interface ContactResponse {
  contact_id: string;
  display_name: string;
  phone_number: string;
  is_registered: boolean;
  registered_user_id?: string | undefined;
  profile_picture?: string | undefined;
  status?: string | undefined;
  last_seen?: Date | undefined;
  added_at: Date;
  updated_at: Date;
}

export interface BulkContactsResponse {
  success: boolean;
  message: string;
  contacts_processed: number;
  contacts_registered: number;
  contacts: ContactResponse[];
}

export interface ContactSyncResponse {
  success: boolean;
  contacts: ContactResponse[];
  total_contacts: number;
  registered_contacts: number;
}

export interface CreateStatusRequest {
  content_type: 'text' | 'image' | 'video';
  content?: string; // For text status content
  content_url?: string; // For image/video URLs
  caption?: string;
  background_color?: string;
  text_color?: string; // Add text color support
  font_style?: string;
  privacy_setting?: 'Everyone' | 'Contacts' | 'ContactsExcept' | 'OnlyShare';
  allowed_contacts?: string[];
  blocked_contacts?: string[];
}

export interface StatusResponse {
  id: string;
  user_id: string;
  content_type: 'text' | 'image' | 'video';
  content?: string | undefined; // For text status content
  content_url?: string | undefined; // For image/video URLs
  caption?: string | undefined;
  background_color?: string | undefined;
  text_color?: string | undefined; // Add text color support
  font_style?: string | undefined;
  privacy_setting: 'Everyone' | 'Contacts' | 'ContactsExcept' | 'OnlyShare';
  view_count: number;
  is_active: boolean;
  expires_at: Date;
  created_at: Date;
  display_name?: string | undefined;
  phone_number?: string | undefined;
  profile_picture?: string | undefined;
}

export interface StatusSummary {
  user_id: string;
  display_name?: string | undefined;
  phone_number?: string | undefined;
  profile_picture?: string | undefined;
  total_statuses: number;
  unviewed_statuses: number;
  latest_status_time: Date;
}

export interface StatusViewResponse {
  viewer_id: string;
  viewed_at: Date;
  display_name?: string | undefined;
  phone_number?: string | undefined;
  profile_picture?: string | undefined;
}

export interface StatusPrivacySettings {
  default_privacy_setting: 'Everyone' | 'Contacts' | 'ContactsExcept' | 'OnlyShare';
  blocked_contacts: string[];
  allowed_contacts: string[];
}

export interface UpdateStatusPrivacyRequest {
  default_privacy_setting?: 'Everyone' | 'Contacts' | 'ContactsExcept' | 'OnlyShare';
  blocked_contacts?: string[];
  allowed_contacts?: string[];
}

export interface ZengoUserInfo {
  UserId: string;
  UserName?: string;
  UserAvatar?: string;
}

export interface ZengoUserRegisterRequest {
  UserInfo: ZengoUserInfo[];
}

export interface ZengoUserRegisterResponse {
  Code: number;
  Message: string;
  RequestId: string;
  ErrorList?: ZengoErrorItem[];
}

export interface ZengoErrorItem {
  UserId: string;
  SubCode: number;
  SubMessage?: string;
}

export interface ZengoUserInfoRequest {
  UserIds: string[];
}

export interface ZengoUserInfoResponse {
  Code: number;
  Message: string;
  RequestId: string;
  Result?: ZengoUserInfoResult[];
  ErrorList?: ZengoErrorItem[];
}

export interface ZengoUserInfoResult {
  UserId: string;
  UserName: string;
  UserAvatar: string;
  Extra: string;
}

export interface ZengoOnlineStatusResponse {
  Code: number;
  Message: string;
  RequestId: string;
  Result?: ZengoOnlineStatusResult[];
  ErrorList?: ZengoErrorItem[];
}

export interface ZengoOnlineStatusResult {
  UserId: string;
  Status: 'Online' | 'Offline';
}

export interface ZengoApiRequest {
  Action: string;
  AppId: string;
  SignatureNonce: string;
  Timestamp: number;
  Signature: string;
  SignatureVersion: string;
}

export interface ZengoModifyUserRequest {
  UserInfo: ZengoModifyUserInfo[];
}

export interface ZengoModifyUserInfo {
  UserId: string;
  UserName?: string;
  UserAvatar?: string;
  Extra?: string;
}

export interface ZengoAddFriendsRequest {
  FromUserId: string;
  FriendInfos: ZengoFriendInfo[];
}

export interface ZengoFriendInfo {
  UserId: string;
  Wording?: string;
  FriendAlias?: string;
  FriendTime?: number;
  Attributes?: ZengoFriendAttribute[];
}

export interface ZengoFriendAttribute {
  Key: 'k0' | 'k1' | 'k2' | 'k3' | 'k4';
  Value: string;
}

export interface ZengoSendFriendRequestRequest {
  FromUserId: string;
  FriendInfos: ZengoFriendRequestInfo[];
}

export interface ZengoFriendRequestInfo {
  UserId: string;
  Wording?: string;
  FriendAlias?: string;
  CreateTime?: number;
  UpdateTime?: number;
  Attributes?: ZengoFriendAttribute[];
}

export interface ZengoDeleteFriendsRequest {
  FromUserId: string;
  UserIds: string[];
  DeleteType: 0 | 1; // 0: two-way, 1: one-way
}

export interface ZengoDeleteAllFriendsRequest {
  FromUserId: string;
  DeleteType: 0 | 1; // 0: two-way, 1: one-way
}

export interface ZengoQueryFriendListRequest {
  FromUserId: string;
  Limit: number;
  Next: number;
}

export interface ZengoQueryFriendListResponse {
  Code: number;
  Message: string;
  RequestId: string;
  TotalCount?: number;
  Next?: number;
  FriendInfos?: ZengoFriendListItem[];
  ErrorList?: ZengoErrorItem[];
}

export interface ZengoFriendListItem {
  UserId: string;
  UserName: string;
  Avatar: string;
  Wording: string;
  FriendAlias: string;
  CreateTime: number;
  Attributes: ZengoFriendAttribute[];
}

export interface ZengoUpdateFriendsAliasRequest {
  FromUserId: string;
  UserIds: ZengoFriendAliasUpdate[];
}

export interface ZengoFriendAliasUpdate {
  UserId: string;
  FriendAlias?: string;
}

export interface ZengoUpdateFriendAttributesRequest {
  FromUserId: string;
  UserId: string;
  Attributes: ZengoFriendAttribute[];
  Action?: number; // 0: set attribute
}

export interface ZengoBlockUsersRequest {
  FromUserId: string;
  UserIds: string[];
}

export interface ZengoUnblockUsersRequest {
  FromUserId: string;
  UserIds: string[];
}

export interface ZengoQueryBlocklistRequest {
  FromUserId: string;
  Limit: number;
  Next: number;
}

export interface ZengoQueryBlocklistResponse {
  Code: number;
  Message: string;
  RequestId: string;
  TotalCount?: number;
  Next?: number;
  Blacklist?: ZengoBlockedUser[];
}

export interface ZengoBlockedUser {
  UserId: string;
  UserName: string;
  Avatar: string;
  UpdateTime: number;
}

export interface ZengoCheckBlockshipRequest {
  FromUserId: string;
  UserIds: string[];
}

export interface ZengoCheckBlockshipResponse {
  Code: number;
  Message: string;
  RequestId: string;
  Succ?: ZengoBlockshipResult[];
  ErrList?: ZengoErrorItem[];
}

export interface ZengoBlockshipResult {
  UserId: string;
  Result: 0 | 1; // 0: not blocked, 1: blocked
}

export interface ViewStatusRequest {
  status_id: string;
}

// Response Interfaces
export interface UserProfileResponse {
  id: string;
  email?: string;
  username?: string;
  phone: string;
  profile_picture?: string;
  is_verified: boolean;
  last_seen?: Date;
  status?: string;
  bio?: string;
  address?: string;
  created_at: Date;
  updated_at: Date;
}

export interface ContactListResponse {
  contacts: ContactResponse[];
  total_count: number;
  registered_count: number;
}

export interface CallHistoryResponse {
  calls: {
    id: string;
    contact_id: string;
    contact_name: string;
    call_type: 'audio' | 'video';
    duration_seconds: number;
    timestamp: Date;
    status: 'completed' | 'missed' | 'declined';
  }[];
  total_count: number;
}

export interface MessageHistoryResponse {
  messages: {
    id: string;
    sender_id: string;
    receiver_id: string;
    message: string;
    message_type: 'text' | 'image' | 'video' | 'audio' | 'document';
    media_url?: string;
    timestamp: Date;
    read: boolean;
    delivered: boolean;
  }[];
  total_count: number;
  unread_count: number;
}

export interface SearchUsersResponse {
  users: {
    id: string;
    username?: string;
    phone: string;
    profile_picture?: string;
    status?: string;
    is_contact: boolean;
  }[];
  total_count: number;
}

export interface GroupResponse {
  id: string;
  name: string;
  description?: string;
  group_picture?: string;
  admin_ids: string[];
  member_ids: string[];
  created_at: Date;
  updated_at: Date;
}

export interface NotificationSettingsResponse {
  message_notifications: boolean;
  call_notifications: boolean;
  group_notifications: boolean;
  status_notifications: boolean;
  sound_enabled: boolean;
  vibration_enabled: boolean;
  notification_tone?: string;
}

export interface PrivacySettingsResponse {
  profile_photo_visibility: 'everyone' | 'contacts' | 'nobody';
  last_seen_visibility: 'everyone' | 'contacts' | 'nobody';
  status_visibility: 'everyone' | 'contacts' | 'nobody';
  read_receipts_enabled: boolean;
  typing_indicators_enabled: boolean;
}

export interface BackupResponse {
  backup_id: string;
  backup_url: string;
  file_size: number;
  created_at: Date;
  expires_at: Date;
}

export interface SystemStatsResponse {
  total_users: number;
  active_users_24h: number;
  total_messages: number;
  total_calls: number;
  server_uptime: number;
  version: string;
}

// Error Response Interface
export interface ErrorResponse {
  success: false;
  message: string;
  error: {
    code: string;
    timestamp: string;
    details?: any;
  };
}
